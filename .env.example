# ================================
# MSW (Mock Service Worker) 配置
# ================================

# 是否启用 MSW 模拟接口
# 开发环境建议: true (方便调试和测试)
# 生产环境建议: false (使用真实接口)
VITE_MSW_ENABLED=false

# 是否启用账户模块模拟
# true: 拦截 /account/* 相关接口
# false: 放行到真实后端
VITE_MSW_ACCOUNT=true

# 是否启用钱包模块模拟
# true: 拦截 /user/amount/* 相关接口
# false: 放行到真实后端
VITE_MSW_WALLET=true

# ================================
# 使用说明
# ================================
# 1. 复制此文件为 .env.local 进行本地开发配置
# 2. 复制此文件为 .env.production 进行生产环境配置
# 3. 不要直接修改 .env.example 文件
