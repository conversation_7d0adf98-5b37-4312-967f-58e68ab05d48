# MSW 使用指南

## 🎯 概述

MSW (Mock Service Worker) 已集成到项目中，可以在开发环境中拦截和模拟 API 请求，无需修改业务代码。

## 🔧 配置管理

### 环境变量配置

**开发环境配置（.env.local）：**
```bash
# 启用/禁用 MSW
VITE_MSW_ENABLED=true

# 控制具体模块
VITE_MSW_ACCOUNT=true  # 账户模块
VITE_MSW_WALLET=true   # 钱包模块
```

**生产环境配置（.env.production）：**
```bash
# 生产环境禁用 MSW
VITE_MSW_ENABLED=false
VITE_MSW_ACCOUNT=false
VITE_MSW_WALLET=false
```

**快速设置：**
```bash
# 复制配置模板
cp .env.example .env.local

# 编辑本地配置
vim .env.local
```

### 浏览器控制台配置

在开发环境中，可以通过浏览器控制台动态控制：

```javascript
// 切换 MSW 总开关
window.mswConfig.toggle()

// 切换特定模块
window.mswConfig.toggleModule('account')  // 账户模块
window.mswConfig.toggleModule('wallet')   // 钱包模块

// 查看当前配置
window.mswConfig.get()

// 设置配置
window.mswConfig.set({
  enabled: true,
  modules: {
    account: true,
    wallet: false
  }
})

// 重置配置
window.mswConfig.reset()
```

## 📦 模块说明

### 账户模块 (Account)

**拦截的接口：**
- `GET /account/get/` - 获取账户信息
- `GET /account/setting/ns_user/get/` - 获取账户设置
- `POST /account/setting/ns_user/reset/` - 保存账户设置

**Mock 场景：**
```javascript
// 访客模式
fetch('/account/get/?mock=guest')

// VIP 用户
fetch('/account/get/?mock=vip')

// 新用户
fetch('/account/get/?mock=new')

// 指定语言的设置
fetch('/account/setting/ns_user/get/?lang=zh')
```

### 钱包模块 (Wallet)

**拦截的接口：**
- `GET /user/amount/` - 获取钱包信息
- `GET /user/amount/:currency` - 获取特定货币余额

**Mock 场景：**
```javascript
// 空钱包
fetch('/user/amount/?mock=empty')

// 富豪钱包
fetch('/user/amount/?mock=rich')

// 只显示特定货币
fetch('/user/amount/?mock=single&currency=BTC')

// 异常状态
fetch('/user/amount/?mock=abnormal')

// 获取特定货币
fetch('/user/amount/BTC')
```

## 🚀 使用方法

### 1. 启用 MSW

**方法一：环境变量**
```bash
# 在 .env.local 中设置
VITE_MSW_ENABLED=true
```

**方法二：浏览器控制台**
```javascript
window.mswConfig.toggle()
```

### 2. 开发流程

1. **正常开发**：MSW 默认禁用，使用真实接口
2. **需要模拟时**：启用 MSW，自动拦截指定接口
3. **测试不同场景**：使用 URL 参数切换 mock 场景
4. **调试完成**：禁用 MSW，恢复真实接口

### 3. 实际使用示例

```typescript
// 在你的业务代码中（无需修改）
import { getAccount } from '@/services/account';
import { getWallet } from '@/services/wallet';

// 这些调用会被 MSW 自动拦截（如果启用）
const account = await getAccount();
const wallet = await getWallet();
```

## 🔍 调试技巧

### 查看拦截状态

1. **控制台日志**：
   - `🎭 MSW: 拦截账户信息请求`
   - `🎭 MSW: 拦截钱包信息请求`

2. **Network 面板**：
   - 被拦截的请求会显示 `(from ServiceWorker)`

3. **配置状态**：
   ```javascript
   console.log(window.mswConfig.get());
   ```

### 常见问题

**Q: MSW 没有拦截请求？**
A: 检查配置状态和模块是否启用

**Q: 如何快速切换真实/模拟接口？**
A: 使用 `window.mswConfig.toggle()`

**Q: 如何添加新的 mock 场景？**
A: 修改对应模块的 handlers 文件

## 📁 文件结构

```
src/mocks/
├── config.ts              # 配置管理
├── browser.ts              # 浏览器环境设置
├── handlers.ts             # 主处理器文件
└── handlers/
    ├── account.ts          # 账户模块处理器
    └── wallet.ts           # 钱包模块处理器
```

## 🎯 最佳实践

1. **默认禁用**：避免影响正常开发
2. **按需启用**：只在需要时启用特定模块
3. **场景测试**：使用 URL 参数测试不同场景
4. **及时关闭**：测试完成后及时禁用
5. **团队协作**：通过环境变量统一配置

## 🔄 切换示例

```javascript
// 开发新功能时
window.mswConfig.set({ enabled: false }); // 使用真实接口

// 测试错误处理时
window.mswConfig.set({ enabled: true, modules: { account: true, wallet: false } });

// 测试空数据场景
fetch('/user/amount/?mock=empty');

// 测试富豪用户场景
fetch('/account/get/?mock=vip');
```

这样的设计让你可以在开发过程中灵活地在真实接口和模拟接口之间切换，而无需修改任何业务代码！
