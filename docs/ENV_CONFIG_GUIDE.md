# 环境变量配置指南

## 📁 文件说明

### 环境变量文件优先级
```
.env.local          # 本地开发配置（优先级最高，不会被 git 提交）
.env.development    # 开发环境默认配置
.env.production     # 生产环境默认配置
.env.example        # 配置模板和说明
```

### 各文件作用

| 文件 | 作用 | 是否提交到 git |
|------|------|----------------|
| `.env.example` | 配置模板和文档 | ✅ 提交 |
| `.env.development` | 开发环境默认配置 | ✅ 提交 |
| `.env.production` | 生产环境默认配置 | ✅ 提交 |
| `.env.local` | 个人本地配置 | ❌ 不提交 |

## 🚀 使用方法

### 1. 本地开发设置

**方法一：使用 .env.local（推荐）**
```bash
# 复制模板文件
cp .env.example .env.local

# 编辑本地配置
# 启用 MSW 进行本地调试
VITE_MSW_ENABLED=true
VITE_MSW_ACCOUNT=true
VITE_MSW_WALLET=true
```

**方法二：直接使用开发环境配置**
```bash
# 开发环境会自动使用 .env.development 配置
npm run dev
```

### 2. 生产环境部署

**方法一：使用环境文件**
```bash
# 生产环境会自动使用 .env.production 配置
npm run build
```

**方法二：通过 CI/CD 设置环境变量**
```bash
# 在部署平台设置环境变量
VITE_MSW_ENABLED=false
VITE_MSW_ACCOUNT=false
VITE_MSW_WALLET=false
```

## 🔧 配置选项详解

### MSW 相关配置

```bash
# 主开关：是否启用 MSW
VITE_MSW_ENABLED=true|false

# 模块开关：是否启用特定业务模块的模拟
VITE_MSW_ACCOUNT=true|false  # 账户相关接口
VITE_MSW_WALLET=true|false   # 钱包相关接口
```

### 配置逻辑

1. **MSW 总开关**：`VITE_MSW_ENABLED=false` 时，所有模拟都被禁用
2. **模块开关**：只有在 MSW 启用时，模块开关才生效
3. **优先级**：`.env.local` > `.env.development/.env.production` > `.env.example`

## 📋 不同场景的配置

### 场景 1：完全使用真实接口
```bash
VITE_MSW_ENABLED=false
# 其他配置无关紧要
```

### 场景 2：只模拟账户接口
```bash
VITE_MSW_ENABLED=true
VITE_MSW_ACCOUNT=true
VITE_MSW_WALLET=false
```

### 场景 3：只模拟钱包接口
```bash
VITE_MSW_ENABLED=true
VITE_MSW_ACCOUNT=false
VITE_MSW_WALLET=true
```

### 场景 4：模拟所有接口
```bash
VITE_MSW_ENABLED=true
VITE_MSW_ACCOUNT=true
VITE_MSW_WALLET=true
```

## 🌍 部署平台配置

### Vercel
在 Vercel 控制台的 Environment Variables 中设置：
```
VITE_MSW_ENABLED = false
```

### Netlify
在 `netlify.toml` 中设置：
```toml
[build.environment]
  VITE_MSW_ENABLED = "false"
```

### Docker
在 Dockerfile 中设置：
```dockerfile
ENV VITE_MSW_ENABLED=false
```

### GitHub Actions
在 workflow 文件中设置：
```yaml
env:
  VITE_MSW_ENABLED: false
```

## 🔍 调试技巧

### 检查当前配置
在浏览器控制台中运行：
```javascript
// 查看当前 MSW 配置
console.log(window.mswConfig?.get());

// 查看环境变量
console.log('MSW_ENABLED:', import.meta.env.VITE_MSW_ENABLED);
console.log('MSW_ACCOUNT:', import.meta.env.VITE_MSW_ACCOUNT);
console.log('MSW_WALLET:', import.meta.env.VITE_MSW_WALLET);
```

### 动态切换配置
```javascript
// 临时启用 MSW
window.mswConfig.set({ enabled: true });

// 临时禁用特定模块
window.mswConfig.toggleModule('account');
```

## ⚠️ 注意事项

1. **不要提交 .env.local**：这个文件包含个人配置，不应该提交到 git
2. **生产环境安全**：确保生产环境中 `VITE_MSW_ENABLED=false`
3. **团队协作**：使用 `.env.development` 统一团队开发配置
4. **环境变量前缀**：Vite 只会暴露以 `VITE_` 开头的环境变量到客户端

## 🎯 最佳实践

1. **开发阶段**：使用 `.env.local` 进行个人配置
2. **测试阶段**：使用 `.env.development` 进行统一配置
3. **生产部署**：确保 `.env.production` 中禁用 MSW
4. **CI/CD**：在部署流水线中显式设置环境变量
5. **文档更新**：修改配置时同步更新 `.env.example`
