# MSW (Mock Service Worker) 使用指南

本项目已成功集成 MSW 框架，用于在开发和测试环境中模拟 API 请求。

## 概述

MSW 是一个 API mocking 库，它通过拦截网络请求来提供模拟响应。本项目提供了一个简化但功能完整的 MSW 集成方案。

## ✅ 当前状态

- ✅ MSW 框架已安装 (v2.10.2)
- ✅ 基础配置已完成
- ✅ 测试环境集成正常工作
- ✅ Mock 数据和处理器已定义
- ✅ 开发环境配置已准备就绪

## 项目结构

```
src/
├── mocks/
│   ├── handlers.ts      # API 处理器定义
│   ├── browser.ts       # 浏览器环境设置
│   └── server.ts        # 测试环境设置
├── test-setup.ts        # 测试环境配置
└── services/
    ├── account.test.ts  # 账户服务测试
    └── wallet.test.ts   # 钱包服务测试
```

## 功能特性

### 1. 开发环境自动启用
- 在开发模式下，MSW 会自动启动
- 所有 API 请求都会被拦截并返回模拟数据
- 在浏览器控制台可以看到 MSW 启用的提示

### 2. 测试环境集成
- 所有测试都会自动使用 MSW
- 每个测试后会重置处理器状态
- 支持在测试中动态修改 API 响应

### 3. 预定义的 API 处理器
- `GET /api/account/get/` - 获取账户信息
- `GET /api/account/setting/ns_user/get/` - 获取账户设置
- `POST /api/account/setting/ns_user/reset/` - 保存账户设置
- `GET /api/user/amount/` - 获取钱包信息

## 使用方法

### 在测试中使用

```typescript
import { describe, it, expect } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '@/mocks/server';
import { getAccount } from './account';

describe('Account Service', () => {
  it('should handle API error', async () => {
    // 临时覆盖默认处理器
    server.use(
      http.get('/api/account/get/', () => {
        return HttpResponse.json({
          code: 500,
          data: null,
          msg: 'Internal server error'
        });
      })
    );

    await expect(getAccount()).rejects.toThrow('Internal server error');
  });
});
```

### 添加新的 API 处理器

在 `src/mocks/handlers.ts` 中添加新的处理器：

```typescript
export const handlers = [
  // 现有处理器...
  
  // 新的 API 处理器
  http.get('/api/new-endpoint/', () => {
    return HttpResponse.json(createApiResponse({
      message: 'Hello from MSW!'
    }));
  }),
  
  http.post('/api/create-item/', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json(createApiResponse({
      id: Date.now(),
      ...body
    }));
  })
];
```

### 模拟不同的响应场景

```typescript
// 成功响应
http.get('/api/data/', () => {
  return HttpResponse.json(createApiResponse(mockData));
});

// 错误响应
http.get('/api/data/', () => {
  return HttpResponse.json(
    createApiResponse(null, 404, 'Not found'),
    { status: 404 }
  );
});

// 网络错误
http.get('/api/data/', () => {
  return HttpResponse.error();
});

// 延迟响应
http.get('/api/data/', async () => {
  await delay(2000); // 延迟 2 秒
  return HttpResponse.json(createApiResponse(mockData));
});
```

## 最佳实践

### 1. 保持 Mock 数据的真实性
- Mock 数据应该尽可能接近真实 API 的响应格式
- 包含所有必要的字段和数据类型
- 考虑边界情况和异常场景

### 2. 测试不同的场景
- 成功响应
- 各种错误状态码
- 网络错误
- 加载状态
- 空数据情况

### 3. 使用类型安全
- 为 Mock 数据定义 TypeScript 类型
- 确保 Mock 数据与实际 API 响应类型一致

### 4. 组织处理器
- 按功能模块组织处理器
- 使用描述性的注释
- 考虑将复杂的处理器拆分到单独的文件

## 调试技巧

### 1. 查看网络请求
- 在浏览器开发者工具的 Network 标签中可以看到被 MSW 拦截的请求
- 请求会显示为来自 Service Worker

### 2. 控制台日志
- MSW 会在控制台输出拦截的请求信息
- 可以在处理器中添加 `console.log` 来调试

### 3. 测试调试
- 使用 `server.listHandlers()` 查看当前注册的处理器
- 使用 `server.resetHandlers()` 重置到初始状态

## 常见问题

### Q: 如何禁用 MSW？
A: 在 `src/entry-client.tsx` 中注释掉 MSW 相关代码，或者设置环境变量。

### Q: 如何在生产环境中确保 MSW 不被包含？
A: MSW 只在开发和测试环境中启用，生产构建时会被自动排除。

### Q: 如何处理认证相关的请求？
A: 在处理器中检查请求头或 cookies，并返回相应的响应。

## 相关资源

- [MSW 官方文档](https://mswjs.io/)
- [MSW GitHub 仓库](https://github.com/mswjs/msw)
- [SolidJS 测试指南](https://docs.solidjs.com/guides/testing)
