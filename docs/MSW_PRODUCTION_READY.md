# MSW 生产就绪配置

## 🎯 完成的功能

### ✅ 已实现的核心功能

1. **业务模块化设计**
   - 账户模块：`src/mocks/handlers/account.ts`
   - 钱包模块：`src/mocks/handlers/wallet.ts`
   - 模块化管理，便于维护

2. **真实接口适配**
   - 基于您的 `services/account.ts` 和 `services/wallet.ts`
   - 使用真实的类型定义 `@/types/account` 和 `@/types/wallet`
   - 完全兼容现有业务代码

3. **灵活的开关控制**
   - 环境变量控制：`VITE_MSW_ENABLED`
   - 模块级控制：`VITE_MSW_ACCOUNT`、`VITE_MSW_WALLET`
   - 浏览器控制台动态切换

4. **多环境配置**
   - 开发环境：`.env.development`
   - 生产环境：`.env.production`
   - 本地配置：`.env.local`

## 🚀 使用方法

### 开发环境

```bash
# 1. 设置本地配置
cp .env.example .env.local

# 2. 编辑配置启用 MSW
echo "VITE_MSW_ENABLED=true" > .env.local

# 3. 启动开发服务器
npm run dev

# 4. 在浏览器控制台查看
# 应该看到：🔶 MSW enabled for development
```

### 生产环境

```bash
# 1. 确保生产配置正确
cat .env.production
# 应该显示：VITE_MSW_ENABLED=false

# 2. 构建生产版本
npm run build

# 3. MSW 将被完全禁用，使用真实接口
```

## 🔄 切换真实/模拟接口

### 方法一：环境变量（推荐）

```bash
# 使用真实接口
VITE_MSW_ENABLED=false

# 使用模拟接口
VITE_MSW_ENABLED=true
```

### 方法二：浏览器控制台

```javascript
// 切换到真实接口
window.mswConfig.set({ enabled: false });

// 切换到模拟接口
window.mswConfig.set({ enabled: true });

// 只模拟账户接口
window.mswConfig.set({ 
  enabled: true, 
  modules: { account: true, wallet: false } 
});
```

## 📦 拦截的接口

### 账户模块
- `GET /account/get/` - 获取账户信息
- `GET /account/setting/ns_user/get/` - 获取账户设置  
- `POST /account/setting/ns_user/reset/` - 保存账户设置

### 钱包模块
- `GET /user/amount/` - 获取钱包信息
- `GET /user/amount/:currency` - 获取特定货币余额

## 🎭 Mock 场景测试

### 账户场景
```javascript
// 访客模式
fetch('/account/get/?mock=guest')

// VIP 用户
fetch('/account/get/?mock=vip')

// 新用户
fetch('/account/get/?mock=new')
```

### 钱包场景
```javascript
// 空钱包
fetch('/user/amount/?mock=empty')

// 富豪钱包
fetch('/user/amount/?mock=rich')

// 异常状态
fetch('/user/amount/?mock=abnormal')
```

## 🔍 验证 MSW 是否工作

### 1. 控制台检查
```javascript
// 查看 MSW 状态
console.log(window.mswConfig.get());

// 查看环境变量
console.log('MSW启用:', import.meta.env.VITE_MSW_ENABLED);
```

### 2. 网络面板检查
- 打开开发者工具 → Network 标签
- 被 MSW 拦截的请求会显示 `(from ServiceWorker)`
- 真实请求会显示正常的网络状态

### 3. 控制台日志
```
🔶 MSW enabled for development
🎭 MSW: 拦截账户信息请求
🎭 MSW: 拦截钱包信息请求
```

## 📋 部署检查清单

### 开发环境 ✅
- [ ] `.env.local` 中 `VITE_MSW_ENABLED=true`
- [ ] 浏览器控制台显示 MSW 启用消息
- [ ] 接口返回模拟数据
- [ ] 可以通过控制台动态切换

### 生产环境 ✅
- [ ] `.env.production` 中 `VITE_MSW_ENABLED=false`
- [ ] 构建后的代码不包含 MSW 相关逻辑
- [ ] 接口请求真实后端
- [ ] 无 MSW 相关控制台日志

## 🛡️ 安全考虑

1. **生产环境隔离**
   - MSW 只在 `VITE_MSW_ENABLED=true` 时加载
   - 生产环境默认禁用，避免意外启用

2. **代码分离**
   - Mock 代码与业务代码完全分离
   - 不影响现有的 `services/account.ts` 和 `services/wallet.ts`

3. **类型安全**
   - 使用真实的类型定义
   - 确保 Mock 数据与真实数据结构一致

## 🎉 总结

现在您拥有了一个完整的 MSW 解决方案：

1. **无侵入性**：不需要修改任何现有业务代码
2. **灵活控制**：可以随时在真实/模拟接口间切换
3. **模块化**：按业务模块组织，便于维护
4. **生产就绪**：完整的环境配置和部署方案
5. **类型安全**：基于真实类型定义，确保数据一致性

您可以立即开始使用：
- 开发时启用 MSW 进行调试和测试
- 生产时禁用 MSW 使用真实接口
- 通过 URL 参数测试不同的业务场景
