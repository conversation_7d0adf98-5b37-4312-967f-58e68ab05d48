import eslint from "@eslint/js";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import * as parser from "@typescript-eslint/parser";
import eslintConfigPrettier from "eslint-config-prettier";
import importPlugin from "eslint-plugin-import";
import jsxA11y from "eslint-plugin-jsx-a11y";
import perfectionist from "eslint-plugin-perfectionist";
import eslintPluginPrettier from "eslint-plugin-prettier/recommended";
import solid from "eslint-plugin-solid/configs/recommended";
import solidts from "eslint-plugin-solid/configs/typescript";
import globals from "globals";
import tseslint from "typescript-eslint";

export default [
  // ✅ 基础推荐配置
  eslint.configs.recommended,
  tseslint.configs.eslintRecommended,
  ...tseslint.configs.recommended,
  eslintPluginPrettier,
  eslintConfigPrettier,
  importPlugin.flatConfigs.recommended,
  perfectionist.configs["recommended-natural"],
  jsxA11y.flatConfigs.recommended,
  solid,

  // ✅ 禁用 sort-imports 在 .mjs 和配置文件中
  {
    files: ["eslint.config.*", "**/*.mjs"],
    rules: {
      "perfectionist/sort-imports": "off"
    }
  },

  // ✅ 启用 sort-imports，仅限源码文件
  {
    files: ["src/**/*.{ts,tsx,js,jsx}"],
    rules: {
      "perfectionist/sort-imports": [
        "error",
        {
          ignoreCase: true,
          internalPatterns: "^(?:@/|src/)", // Change to array format
          newlinesBetween: "always",
          order: "asc",
          type: "natural",
          groups: [
            ["builtin", "builtin-type"],
            ["external", "external-type"],
            ["internal", "internal-type"],
            ["parent", "parent-type"],
            ["sibling", "sibling-type"],
            ["index", "index-type"],
            ["side-effect", "side-effect-style"],
            ["object", "unknown"]
          ]
        }
      ]
    }
  },

  // ✅ 主语言和规则配置（TS + Solid）
  {
    plugins: {
      "@typescript-eslint": typescriptEslint
    },
    ...solidts,
    languageOptions: {
      globals: Object.fromEntries(
        Object.entries(globals.browser).filter(([key]) => key.trim() === key)
      ),
      parser: parser,
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname
      }
    },
    rules: {
      "@typescript-eslint/no-explicit-any": "off",
      "perfectionist/sort-array-includes": "off",
      "perfectionist/sort-interfaces": "off",
      "perfectionist/sort-classes": "off",
      "perfectionist/sort-enums": "off",
      "perfectionist/sort-intersection-types": "off",
      "perfectionist/sort-jsx-props": "off",
      "perfectionist/sort-variable-declarations": "off",
      "perfectionist/sort-maps": "off",
      "perfectionist/sort-object-types": "off",
      "perfectionist/sort-objects": "off",
      "perfectionist/sort-sets": "off",
      "prettier/prettier": [
        "error",
        {
          endOfLine: "auto"
        }
      ]
    },
    settings: {
      "import/resolver": {
        typescript: {
          node: true,
          typescript: true
        }
      }
    }
  }
];
