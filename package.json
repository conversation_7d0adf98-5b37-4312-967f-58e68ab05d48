{"name": "example-with-vitest", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start", "test": "vitest run", "test-watch": "vitest", "test-ui": "vitest --ui"}, "type": "module", "devDependencies": {"@solidjs/meta": "^0.29.4", "@solidjs/router": "^0.15.0", "@solidjs/start": "^1.1.4", "@solidjs/testing-library": "^0.8.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitest/ui": "3.2.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.14.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-solid": "^0.14.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "jsdom": "26.1.0", "msw": "^2.10.2", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "solid-js": "^1.9.7", "typescript": "^5.6.3", "typescript-eslint": "^8.34.0", "vinxi": "^0.5.7", "vite": "^6.0.0", "vite-plugin-solid": "^2.11.6", "vitest": "3.2.3"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@solid-primitives/promise": "^1.1.1", "@solid-primitives/scheduled": "^1.5.1", "@solid-primitives/timer": "^1.4.1", "@tailwindcss/vite": "^4.1.8", "axios": "^1.9.0", "decimal.js": "^10.5.0", "detect-gpu": "^5.0.70", "long": "^5.3.2", "murmurhash": "^2.0.1", "protobufjs": "^7.5.3", "socket.io-client": "^4.8.1", "socket.io-msgpack-parser": "^3.0.2", "tailwindcss": "^4.1.8"}, "msw": {"workerDirectory": ["public"]}}