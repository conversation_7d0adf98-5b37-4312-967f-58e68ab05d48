import { MetaProvider } from "@solidjs/meta";
import { onMount } from "solid-js";
import { getHostType } from "./common/utils";
import { env, setEnv } from "./common/storage/env";
import { MetaTags } from "./sections/meta";
import { AppBase } from "./sections/app";
import "./css/app.css";

function initializeTheme() {
  onMount(() => {
    const { isBrAuditHost } = getHostType(env.host);

    if (isBrAuditHost) {
      document.documentElement.classList.add("yellowtheme");
    }
  });
}

function mxLang() {
  const { isMxHost } = getHostType(env.host);
  if (isMxHost) return "es";
}

export default function App() {
  initializeTheme();
  const lang = mxLang();

  return (
    <MetaProvider>
      <MetaTags />
      <AppBase />
    </MetaProvider>
  );
}
