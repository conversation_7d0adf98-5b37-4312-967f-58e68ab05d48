import axios, { AxiosError, AxiosInstance, AxiosResponse } from "axios";

class ErrorWithTip extends Error {
  tip?: string;
  constructor(message: string, tip?: string) {
    super(message);
    this.tip = tip;
  }
}

class HttpResponseError extends <PERSON>rror {
  code: number | string;
  response: any;

  constructor(message: string, code: number | string, response: any) {
    super(message);
    this.code = code;
    this.response = response;
  }
}

type ApiResponse<T = any> = {
  code: number | string;
  data: T;
  msg: string;
};

let httpReject: any = null;
export function registHttpReject(httpReject: any) {
  httpReject = httpReject;
}

let http: AxiosInstance | null = null;
export function getHttp() {
  if (!http) {
    http = axios.create({
      baseURL: "/api",
    });

    http.interceptors.request.use(
      (config) => {
        if ((config as any).cache) {
          config.baseURL = "/cache";
          delete (config as any).cache;
        }
        config.headers = config.headers || {};
        return config;
      },
      (error) => Promise.reject(String(error) + ":REQ!")
    );

    http.interceptors.response.use(
      <T>(response: AxiosResponse<ApiResponse<T>>): T => {
        const data = response.data;
        if (data.code === 0) {
          return data.data;
        }
        if (data.code === 403 || data.code === "403") {
          window.location.reload();
        }
        return Promise.reject(
          new HttpResponseError(data.msg, data.code, data)
        ) as T;
      },
      (error: AxiosError) => {
        if (error && error.response) {
          return Promise.reject(
            new ErrorWithTip(
              "Oops!We lost your network, Please have a check!",
              `${error.response.status}${error.config?.url}`
            )
          );
        }
        return Promise.reject(new ErrorWithTip("Network error!"));
      }
    );

    http.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (httpReject && typeof httpReject === "function") {
          await httpReject(error);
        }
        return Promise.reject(error);
      }
    );
  }
  return http;
}
