import { Manager, ManagerOptions, Socket } from "socket.io-client";
import Long from "long";
import customParser from "socket.io-msgpack-parser";
import { isServer } from "solid-js/web";
import { GenericResponse, GenericResponseSchema } from "../pb/common_pb";
import { DescMessage, fromBinary, Message, toBinary } from "@bufbuild/protobuf";

interface URIConfig {
  socket: string;
  test?: string;
}

interface SignData {
  sign: string;
  uri: URIConfig;
  source?: string;
}

type Decoder = "utf8" | "json" | "int8" | "int32" | "int64" | DescMessage;

type Encoder = "utf8" | "json" | "int8" | "int32" | "int64" | DescMessage;

type AfterRequestCallback = (data: any) => void;

const textEncoder = new TextEncoder();
const textDecoder = new TextDecoder();

class SocketError extends Error {
  code: number;

  constructor(message: string, code: number = 0) {
    super(message);
    this.code = code;
  }
}

export class SocketManager extends Manager {
  static socketOptions: Partial<ManagerOptions> = {
    timeout: 20000,
    reconnectionDelayMax: 10000,
    transports: ["websocket"],
    autoConnect: false,
    parser: customParser,
    query: {}
  };

  private afterRequest: AfterRequestCallback[];
  private uris: URIConfig[];
  private isConnecting;
  public enableSocketConnect: () => void = () => {};
  private waitSocketConnect: Promise<void>;
  public latency = 0;

  constructor(
    { uris }: { uris: URIConfig[] },
    opts: Partial<ManagerOptions> = SocketManager.socketOptions
  ) {
    super(uris[0].socket, opts);
    this.afterRequest = [];
    this.uris = uris;
    this.isConnecting = false;
    this.waitSocketConnect = new Promise(resolve => {
      this.enableSocketConnect = resolve;
    });
    this.latency = 0;
    this.socketRequestBind = this.socketRequestBind.bind(this);

    (this as any).once("pong", (initialLatency: number) => {
      let latency = initialLatency;
      (this as any).on("pong", (currentLatency: number) => {
        latency = 0.2 * currentLatency + 0.8 * latency;
        this.latency = latency;
      });
    });
  }

  private socketRequestBind(socket: Socket) {
    return (event: string, data: any): Promise<any> => {
      return new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          return reject(new Error(`${(socket as any).nsp}/${event}:Socket time out`));
        }, 20000);

        socket.emit(
          event,
          data,
          this.decodeBind((resp: GenericResponse) => {
            clearTimeout(timer);
            try {
              for (let i = 0; i < this.afterRequest.length; i++) {
                this.afterRequest[i](resp);
              }
              if (resp.code === 0) {
                resolve(resp.data);
              } else {
                console.log(`Socket Error: ${(socket as any).nsp}:${event}${JSON.stringify(data)}`);
                reject(new SocketError(resp.msg, resp.code));
              }
            } catch (err) {
              reject(err);
            }
          }, GenericResponseSchema)
        );
      });
    };
  }

  open(fn?: (err?: Error) => void): this {
    if (!isServer && !this.isConnecting && this._readyState.indexOf("open") < 0) {
      this.isConnecting = true;
      this.openWithSign().finally(() => {
        super.open(fn);
        this.isConnecting = false;
      });
    }
    return this;
  }

  private async openWithSign() {
    try {
      const { sign, uri, source } = await this.getSignData();
      await this.waitSocketConnect;
      this.opts.query = {
        ...this.opts.query,
        p: sign,
        t: source
      };
      (this as any).uri = uri.socket;
    } catch {
      // ignore errors
    }
  }

  private async testSocketRoute(uri: URIConfig, param: string = ""): Promise<SignData> {
    const endpoint = `${uri.test ?? uri.socket}/test/${param ? `?p=${param}` : ""}`;
    const response = await fetch(endpoint, { credentials: "include" });
    const sign = await response.text();
    return { uri, sign };
  }

  private async getSignData() {
    const ua = window.navigator.userAgent.trim();
    const seed = await generateSeedSignature(ua);
    let { uri, sign } = await Promise.race(this.uris.map(uri => this.testSocketRoute(uri, seed)));
    const signed = await signWithUserAgent(sign, ua);
    return {
      sign: signed,
      uri: uri,
      source: sign
    };
  }

  socket(nsp: string) {
    const socket = super.socket(nsp);
    (socket as any).request = this.socketRequestBind(socket);
    return socket;
  }

  addAfterRequest(fn: AfterRequestCallback) {
    this.afterRequest.push(fn);
  }

  decode(decoder: Decoder = "utf8") {
    return (data: ArrayBuffer) => {
      if (decoder === "utf8") {
        return textDecoder.decode(data);
      } else if (decoder === "json") {
        return JSON.parse(textDecoder.decode(data));
      } else if (decoder === "int8") {
        return new DataView(data).getUint8(0);
      } else if (decoder === "int32") {
        return new DataView(data).getUint32(0);
      } else if (decoder === "int64") {
        const view = new DataView(data);
        return new Long(view.getUint32(4), view.getUint32(0)).toNumber();
      } else {
        return fromBinary(decoder, new Uint8Array(data));
      }
    };
  }

  encode(encoder: Encoder = "utf8") {
    return (data: any): Uint8Array => {
      if (encoder === "utf8") {
        return textEncoder.encode(data);
      }
      if (encoder === "json") {
        return textEncoder.encode(JSON.stringify(data));
      }

      if (encoder === "int8") {
        return Uint8Array.of(data);
      }
      if (encoder === "int32") {
        const buf = new Uint8Array(4);
        new DataView(buf.buffer).setUint32(0, data);
        return buf;
      } else if (encoder === "int64") {
        const long = Long.fromNumber(data);
        const buf = new Uint8Array(8);
        const view = new DataView(buf.buffer);
        view.setUint32(0, long.high);
        view.setUint32(4, long.low);
        return buf;
      } else {
        return toBinary(encoder, data);
      }
    };
  }

  decodeBind(callback: (data: GenericResponse) => void, decoder: Decoder = "utf8") {
    const fn = this.decode(decoder);
    return (buffer: ArrayBuffer) => callback(fn(buffer));
  }
}

export let manager: SocketManager | null = null;
export function getSocket(nsp?: string) {
  if (manager === null) {
    manager = new SocketManager({
      uris: getSocketUri()
    });
  }
  return nsp ? manager.socket(nsp) : manager;
}

function getSocketUri() {
  const { protocol, hostname } = isServer ? { protocol: "", hostname: "" } : location;
  if (protocol === "http:" && ["dev", "localhost", "192"].find(str => hostname.startsWith(str)))
    return [
      {
        socket: `${origin}`,
        test: `${origin}/socketapi`
      }
    ];
  {
    const match = hostname.match(/[^\.]+\.\w+$/);
    return [
      {
        socket: `${protocol}//socketv4.${match}`
      }
    ];
  }
}

async function generateSeedSignature(userAgent: string): Promise<string> {
  const now = Date.now();
  const input = `${userAgent}:${now}`;
  const hashBuffer = await crypto.subtle.digest("SHA-256", textEncoder.encode(input));
  return bufferToHex(hashBuffer);
}

async function signWithUserAgent(sign: string, userAgent: string): Promise<string> {
  const key = await crypto.subtle.importKey(
    "raw",
    textEncoder.encode(userAgent),
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  );
  const sigBuffer = await crypto.subtle.sign("HMAC", key, textEncoder.encode(sign));
  return bufferToHex(sigBuffer);
}

function bufferToHex(buffer: ArrayBuffer): string {
  return [...new Uint8Array(buffer)].map(b => b.toString(16).padStart(2, "0")).join("");
}
