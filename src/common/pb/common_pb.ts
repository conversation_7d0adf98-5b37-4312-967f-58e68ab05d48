// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file common.proto (package common, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file common.proto.
 */
export const file_common: GenFile = /*@__PURE__*/
  fileDesc("Cgxjb21tb24ucHJvdG8SBmNvbW1vbiI6Cg9HZW5lcmljUmVzcG9uc2USDAoEY29kZRgBIAEoERILCgNtc2cYAiABKAkSDAoEZGF0YRgDIAEoDGIGcHJvdG8z");

/**
 * @generated from message common.GenericResponse
 */
export type GenericResponse = Message<"common.GenericResponse"> & {
  /**
   * @generated from field: sint32 code = 1;
   */
  code: number;

  /**
   * @generated from field: string msg = 2;
   */
  msg: string;

  /**
   * @generated from field: bytes data = 3;
   */
  data: Uint8Array;
};

/**
 * Describes the message common.GenericResponse.
 * Use `create(GenericResponseSchema)` to create a new message.
 */
export const GenericResponseSchema: GenMessage<GenericResponse> = /*@__PURE__*/
  messageDesc(file_common, 0);

