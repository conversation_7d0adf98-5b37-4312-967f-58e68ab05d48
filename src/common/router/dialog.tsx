import { NavigatorContext, useNavigate } from "@/context/navigator-context";
import { RouteDefinition, useLocation } from "@solidjs/router";
import { useContext, VoidComponent } from "solid-js";
import { createStore } from "solid-js/store";

interface DialogStackRouterProps {
  routes: RouteDefinition[];
}

export const DialogStackRouter: VoidComponent<DialogStackRouterProps> = L => {
  const O = useLocation();
  const M = useNavigate();
  const { action: I } = useContext(NavigatorContext)!;
  const N = useRouteDefinitions();
  const R = getGuardByMatchs(N(), O);
  const H = N()[0];
  let z = isDialogRoute(H);
  const X = z || R;
  const [W, K] = createStore({
    list: [
      {
        pathname: X ? "/" : O.pathname,
        search: X ? "" : O.search,
        state: X ? null : O.state,
        matchs: L.routes.filter(ne => !isDialogRoute(ne))
      }
    ]
  });
  createComputed(() => {
    O.hash.startsWith("#/") &&
      M(O.hash.slice(1), {
        replace: !0
      });
  });
  function G() {
    var ne;
    if (depth.init === history.length) {
      const se = ((ne = history.state) == null ? void 0 : ne._depth) || history.length - 1;
      history.replaceState(
        {
          _depth: se
        },
        "",
        routerUtils.getLangPrefix(setting.lang)
      ),
        history.pushState(
          {
            _depth: se + 1
          },
          "",
          O.pathname + O.search
        );
    }
  }
  X && G();
  const Y = new WeakSet(),
    [Z, J] = createSignal(),
    Q = createMemo(ne => {
      const se = N(),
        he = firstRoute(se),
        me = untrack(() => {
          if (O.hash.startsWith("#/")) return ne;
          const le = getGuardByMatchs(se, O);
          if (le)
            return (
              M(le, {
                replace: !0
              }),
              ne
            );
          const pe = I(),
            de = O.pathname;
          let ce = [...W.list];
          pe === "POP" && ne && isDialogRoute(ne.matchs[0]) && Y.add(unwrap(ne));
          function fe(ye) {
            return pe !== "PUSH"
              ? ye
              : untrack(() =>
                  ye.filter(ve => {
                    const Ce = unwrap(ve);
                    return Y.has(Ce) ? (Y.delete(Ce), !1) : !0;
                  })
                );
          }
          const ue = {
            pathname: de,
            search: O.search,
            state: O.state && {
              ...O.state
            }
          };
          if (isDialogRoute(se[0])) {
            const ye = ce.findIndex(
              pe === "REPLACE"
                ? ve => firstRoute(ve.matchs) === he
                : ve => ve.pathname === ue.pathname && ve.search === ue.search
            );
            if (ye === -1) {
              const ve = {
                ...ue,
                matchs: se
              };
              return (
                (ce = fe(ce).concat(ve)),
                ce.length > MAX_ROUTE_CACHE && ce.splice(1, 1),
                K("list", ce),
                ve
              );
            } else {
              const ve = unwrap(W.list[ye]);
              return Y.has(ve) && Y.delete(ve), K("list", ye, ue), W.list[ye];
            }
          } else
            return (
              batch(() => {
                K("list", fe(ce)), K("list", 0, ue);
              }),
              W.list[0]
            );
        });
      return (
        me &&
          onCleanup(() => {
            var le;
            J(unwrap(me)),
              ((le = he.info) == null ? void 0 : le.keepAlive) === !1 &&
                K("list", pe => pe.filter(de => unwrap(de) !== me));
          }),
        me
      );
    });
  function _() {
    return z ? ((z = !1), !0) : !1;
  }
  function ee() {
    const [ne, se] = createSignal(untrack(() => !env.mobile));
    createMemo(() => N()[0]);
    const he = () => !isDialogRoute(N()[0]);
    return (
      createComputed(() => se(me => me || he())),
      createComponent(Show, {
        get when() {
          return ne();
        },
        get children() {
          return createComponent(ActiveProvider, {
            get value() {
              return he();
            },
            get children() {
              return createComponent(StaticRouter, {
                get state() {
                  return W.list[0];
                }
              });
            }
          });
        }
      })
    );
  }
  function te() {
    const ne = children(() =>
      createComponent(For, {
        get each() {
          return W.list;
        },
        children: (se, he) => {
          if (he() === 0) return null;
          const me = createMemo(() => {
            const le = Q();
            return le ? le.pathname === se.pathname : !1;
          });
          return createComponent(StackProvider.Provider, {
            value: {
              route: se,
              index: he
            },
            get children() {
              return createComponent(ActiveProvider, {
                get value() {
                  return me();
                },
                get children() {
                  return createComponent(DialogRoot, {
                    isInitDialog: _,
                    get preRoute() {
                      return Z();
                    },
                    get children() {
                      return createComponent(StaticRouter, {
                        state: se
                      });
                    }
                  });
                }
              });
            }
          });
        }
      })
    );
    return createListTransition(ne, {
      onChange({ removed: se, finishRemoved: he }) {
        se.length > 0 && setTimeout(() => he(se), 600);
      }
    });
  }
  const ie = () => getDialogConfig(N()),
    re = () => {
      var ne;
      return (ne = getRouteInfo(N())) == null ? void 0 : ne.dialog;
    },
    oe = createMemo(
      ne => {
        const se = ie();
        return re() && se ? getDialogSize(se) : ne;
      },
      [void 0, void 0]
    );
  function ae() {
    let ne, se;
    const he = createMemo(() => isDialogRoute(N()[0]));
    return (
      createEffect(me => {
        const le = he(),
          pe = untrack(() => env.mobile),
          de = [...(pe ? [ROOT_ANE[0], ROOT_ANE[0]] : ROOT_ANE)],
          ce = [...POP_ANE],
          fe =
            me === void 0
              ? {
                  ...DEFAULT_ANE_PROPS,
                  duration: 0
                }
              : DEFAULT_ANE_PROPS;
        if ((ne.classList.add("dialog-visible"), !ne.animate)) return le;
        const ue = ne.animate(le ? de : de.reverse(), fe);
        if (
          (ue.finished.then(() => {
            ne.classList.toggle("dialog-visible", le), ue.cancel();
          }),
          !pe)
        ) {
          const ye = se.animate(pe || le ? ce : ce.reverse(), fe);
          ye.finished.then(() => ye.cancel());
        }
        return le;
      }),
      createComponent(Portal, {
        ref: me => {
          (ne = me),
            (me.className = "dialog-root"),
            createEventListener(
              me,
              "click",
              leading(
                throttle,
                le => {
                  le.target === ne && M(-1);
                },
                600
              )
            );
        },
        get children() {
          var me = _tmpl$$c(),
            le = se;
          return (
            typeof le == "function" ? use(le, me) : (se = me),
            insert(me, te),
            createRenderEffect(
              pe => {
                var de = cx("dialog-list", oe()[0]),
                  ce = oe()[1];
                return de !== pe.e && className(me, (pe.e = de)), (pe.t = style(me, ce, pe.t)), pe;
              },
              {
                e: void 0,
                t: void 0
              }
            ),
            me
          );
        }
      })
    );
  }
  return [createMemo(ee), createComponent(ae, {})];
};
