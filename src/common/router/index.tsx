import { createEffect, untrack } from "solid-js";
import { setSetting, setting } from "../storage/setting";
import { env } from "../storage/env";
import { RouteDefinition, useCurrentMatches } from "@solidjs/router";

const SESSION_LENGTH = "init_length";
function getInitDepth() {
  const storedLength = sessionStorage.getItem(SESSION_LENGTH);
  return storedLength
    ? Number(storedLength)
    : (sessionStorage.setItem(SESSION_LENGTH, String(history.length)), history.length);
}

const depth = {
  init: getInitDepth()
};
const langMatch = /^\/([\w-]+)(.*)$/;
export const routerUtils = {
  langSet: new Set<string>([]),
  getLangPrefix: (language: string) => (language === "en" ? "/" : `/${language}`),
  syncLang: (languages: string[], browserLanguage = navigator.language) => {
    untrack(() => {
      routerUtils.langSet = new Set(languages);
      const currentPath = location.href.slice(location.origin.length);
      if (setting.firstVisit) {
        const { lang: detectedLang } = routerUtils.extractLocale(currentPath);
        if (detectedLang === "en") {
          if (browserLanguage) {
            const languageEntries = Object.entries(env.langs);
            let matchedLanguage = languageEntries.find(entry => entry[1][1] === browserLanguage);
            if (!matchedLanguage) {
              browserLanguage = browserLanguage.split("-")[0];
              matchedLanguage = languageEntries.find(entry =>
                entry[1][1].includes(browserLanguage)
              );
            }
            if (matchedLanguage) {
              setSetting("lang", matchedLanguage[0]);
            }
          }
        } else {
          setSetting("lang", detectedLang);
        }
      }
      history.replaceState(
        history.state,
        "",
        routerUtils.replaceLangPrefix(currentPath, setting.lang)
      );
    });
  },
  replaceLangPrefix(path: string, targetLang: string) {
    const { urlOriginal } = routerUtils.extractLocale(path);
    return (targetLang === "en" ? "" : `/${targetLang}`) + urlOriginal;
  },
  extractLocale(path: string) {
    let detectedLang = "en";
    const match = path.match(langMatch);
    if (match && routerUtils.langSet.has(match[1])) {
      detectedLang = match[1];
      path = match[2] || "/";
      if (!path.startsWith("/")) {
        path = `/${path}`;
      }
    }
    return {
      lang: detectedLang,
      urlOriginal: path
    };
  }
};

export function isLayoutRoute(route: RouteDefinition) {
  return !!(!route.path && route.component);
}

export function resolveRouteDefinition(route: RouteDefinition | RouteDefinition[]) {
  return route instanceof Array ? route : [route];
}

export function useRouteDefinitions() {
  const L = useCurrentMatches();
  return createNoSuspense(createMemo(() => L().map(O => unwrap(O.route.key))));
}
