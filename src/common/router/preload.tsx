import { createEffect, JSX, on, untrack } from "solid-js";
import { memo } from "../utils";
import { RouteDefinition, RoutePreloadFunc } from "@solidjs/router";
import { createTrigger } from "@solid-primitives/trigger";
import PQueue from "p-queue";

const [shouldSchedule, scheduleNextPreload] = createTrigger();

const pendingQueue: string[] = [];
const requestPreload = (path: string) => {
  pendingQueue.push(path);
  scheduleNextPreload();
};
const getPriority = (path: string | string[]) => {
  path = Array.isArray(path) ? path : [path];
  return Math.max(...path.map(O => (O.startsWith("/game") ? 10 : 1)));
};
const preloadQueue = new PQueue({
  concurrency: 1,
  interval: 300,
  autoStart: false
});
preloadQueue.pause();
window.addEventListener("load", () => {
  preloadQueue.start();
});

const PRELOAD_PERIOD = 500;
const createMatcher = memo(<S extends string | string[]>(path: S, partial = true) => {
  const pathArray = Array.isArray(path) ? path : [path];
  return (location: string) => {
    const locSegments = location.split("/").filter(Boolean);
    return pathArray.some((path: string) => {
      const [basePath, splat] = path.split("/*", 2);
      const segments = basePath.split("/").filter(Boolean);
      const len = segments.length;
      const lenDiff = locSegments.length - len;
      if (lenDiff < 0 || (lenDiff > 0 && splat === undefined && !partial)) {
        return null;
      }
      for (let i = 0; i < len; i++) {
        const segment = segments[i];
        const dynamic = segment[0] === ":";
        const locSegment = dynamic ? locSegments[i] : locSegments[i].toLowerCase();
        const key = dynamic ? segment.slice(1) : segment.toLowerCase();
        if (!dynamic && locSegment !== key) {
          return false;
        }
      }
      return true;
    });
  };
});

interface PreloadableRoute extends RouteDefinition {
  component?: {
    preload?: () => void;
  } & RouteDefinition["component"];
}

interface Preload {
  path: string;
  preload: () => void;
}

export const PreloadScheduler = (props: { routes: PreloadableRoute[]; children?: JSX.Element }) => {
  const collectPreloadRoutes = (
    routes: PreloadableRoute | PreloadableRoute[],
    basePath = "",
    preloadList: Preload[] = []
  ) => {
    routes = Array.isArray(routes) ? routes : [routes];
    if (!routes || routes.length === 0) {
      return preloadList;
    }

    routes.forEach(route => {
      let fullPath = basePath + route.path;
      if (route.component?.preload) {
        preloadList.push({
          path: fullPath,
          preload: route.component?.preload
        });
      }

      if (route.children) {
        collectPreloadRoutes(route.children, fullPath, preloadList);
      }
    });
    return preloadList;
  };
  const preloadRoutes = collectPreloadRoutes(props.routes);
  const matchPreloadRoutes = (currentPath: string, routes: Preload[]) => {
    const matchedRoutes: Preload[] = [];
    untrack(() => {
      let route;
      for (let index = 0; index < routes.length; ) {
        route = routes[index];
        if (createMatcher(route.path)(currentPath)) {
          matchedRoutes.push(route);
          [routes[index]] = [routes[routes.length - 1]];
          routes.length--;
        } else {
          index++;
        }
      }
    });

    return matchedRoutes;
  };
  let preloadTimer: NodeJS.Timeout | null = null;
  createEffect(
    on(shouldSchedule, () => {
      if (!preloadTimer) {
        preloadTimer = setTimeout(() => {
          preloadTimer = null;
          const pendingPaths = pendingQueue.slice(0);
          pendingQueue.length = 0;
          const routesToPreload: Preload[] = [];
          for (let i = 0; i < pendingPaths.length; i++) {
            const path = pendingPaths[i];
            const matchedRoutes = matchPreloadRoutes(path, preloadRoutes);
            if (matchedRoutes.length) {
              routesToPreload.push(...matchedRoutes);
            }
          }
          routesToPreload.forEach(route => {
            const priority = getPriority(route.path);
            preloadQueue.add(
              () => {
                route.preload();
              },
              {
                priority: priority
              }
            );
          });
        }, PRELOAD_PERIOD);
      }
    })
  );
  return props.children;
};
