import { createEffect, createRoot, createSignal, untrack } from "solid-js";
import { createPersistedStore } from ".";
import { getSocket, SocketManager } from "../network/socket.io";
import { Socket } from "socket.io-client";
import { createTimer } from "@solid-primitives/timer";
import { getSetting, setSetting, setting } from "./setting";
import { getAccount, saveSetting } from "@/services/account";
import { setEnv } from "./env";
import { AccountSetting, AccountStore, ChatRoomPermission } from "@/types/account";
import { until } from "@solid-primitives/promise";

const defaultChatRoomPermission: ChatRoomPermission = {
  blockable: false,
  deleteable: false,
  vipable: false,
  blockLevel: 0
};

const initData: AccountStore = {
  name: "",
  avatar: "",
  userId: 0,
  uniqueUid: 0,
  uid: 0,
  createTime: 0,
  kyc: 1,
  gameable: true,
  login: false,
  email: "",
  loginSource: "",
  open: 0,
  bonusCurrencyName: "",
  avatarUpdateTime: 0,
  vipLevel: 0,
  currXP: 0,
  levelEndXP: 0,
  levelStartXP: 0,
  safePwd: false,
  google2StepAuth: false,
  hasPhone: false,
  chatRoomPermission: defaultChatRoomPermission,
  emailVerified: false,
  channel: "",
  currentInvitationCode: "",
  areaCode: "",
  showable: true,
  areaAlert: false,
  acceptPromotion: true,
  passwordExist: false,
  invitationUrl: "",
  serverTimestemp: 0,
  ip: "",
  rayId: "",
  passkeyHasSet: false,
  setting: {
    currencyFullName: false,
    localeCurrencyName: "USDFIAT",
    enableLocaleCurrency: false,
    currencyName: "BTC",
    currencyBonusType: "",
    hideSmallCurrency: false,
    lastFiatCurrency: "",
    lastCryptoCurrency: "",
    lastNftCurrency: "",
    soundEffectEnable: true,
    maxbetAlert: true,
    allowRechargeSuccessEmail: 1,
    allowWithdrawSuccessEmail: 1,
    lang: "en"
  },
  redDot: {
    systemNotice: 0,
    gameComment: 0
  }
};

const [account, setAccount, accountInit] = createRoot(() => {
  const [isInitialized, setIsInitialized] = createSignal(false);
  const [accountStore, setAccountStore] = createPersistedStore(initData, {
    name: "account"
  });

  const userSocket = getSocket("/user") as Socket;

  syncAccount()
    .then(() => {
      userSocket.connect();
      getSocket("/game-support").connect();
      (getSocket() as SocketManager).enableSocketConnect();
      setIsInitialized(true);
    })
    .catch(() => {});

  createTimer(() => syncAccount().catch(() => {}), 300000, setInterval);

  createEffect(() => {
    if (accountStore.areaCode === "KR" && setting.firstVisit) {
      setSetting("theme", "light");
    }
  });

  createEffect(() => {
    if (accountStore.login) {
      untrack(() => {
        userSocket.emit("user-login");
        getSetting()
          .then(serverSetting => {
            if (!serverSetting.lang || serverSetting.lang !== setting.lang) {
              serverSetting.lang = setting.lang;
            }
            setAccountStore("setting", serverSetting);
          })
          .catch(() => {});
      });
    }
  });

  createEffect(fn => {
    const setting = JSON.stringify(accountStore.setting);
    if (fn) {
      saveSetting(setting);
    }
    return setting;
  });

  return [accountStore, setAccountStore, isInitialized];
});

async function syncAccount() {
  const account = await getAccount();
  setEnv("timeDiff", Date.now() - (account.serverTimestemp || Date.now()));
  setAccount(account);
}

export async function untilLogin() {
  return until(() => account.login);
}

export { account, setAccount };
