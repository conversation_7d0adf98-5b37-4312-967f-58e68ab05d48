import { throttle } from "@solid-primitives/scheduled";
import { createComputed, createEffect, createRoot, createSignal, untrack } from "solid-js";
import { createStore, Store } from "solid-js/store";
import { isAndroid } from "../utils";
import { isServer } from "solid-js/web";
import murmurHash from "murmurhash";
import { createTimer } from "@solid-primitives/timer";
import { getSocket, SocketManager } from "../network/socket.io";
import { getAccount, preAccountReq, preWalletReq } from "~/services/account";
import { Socket } from "socket.io-client";
import { Account, Wallet } from "~/services/accoun";

// 常量定义
const DESIGN_WIDTH = 430;
const MAX_DESIGN_WIDTH = 640;
const MOBILE_BREAKPOINT = 700;

export interface LocalCurrency {
  currencyCode: string;
  symbol: string;
  areaCode: string;
  enabled: boolean;
  name: string;
}

interface EnvState {
  DESIGN_WIDTH: number;
  MAX_DESIGN_WIDTH: number;
  timeDiff: number;
  freeCoin: string;
  platformCoin: string;
  bcl: string;
  clientWidth: number;
  remScale: number;
  siteName: string;
  host: string;
  mascot: string;
  isLowEndDevice: boolean;
  initSearchParams?: URLSearchParams;
  initHashParams?: string;
  langs: Record<string, unknown>;
  mobile: boolean;
  localCurrencyConfig: any[][];
  readonly serverTime: Date;
  inActive: boolean;
  appName: string;
  isPWA: boolean;
  isAPP: boolean;
  buildHost?: string;
}

const [env, setEnv] = createRoot(() => {
  function getDimensions() {
    if (isServer) {
      return {
        clientWidth: 0,
        remScale: 0
      };
    }
    const clientWidth = document.documentElement.clientWidth;
    const width = clientWidth < MAX_DESIGN_WIDTH ? clientWidth : DESIGN_WIDTH;
    return {
      clientWidth,
      remScale: width / DESIGN_WIDTH
    };
  }

  const viewport = !isServer
    ? [document.documentElement.clientWidth, document.documentElement.clientHeight]
    : [0, 0];

  const [state, setState] = createStore<EnvState>({
    DESIGN_WIDTH,
    MAX_DESIGN_WIDTH,
    timeDiff: 0,
    freeCoin: "FREECOIN",
    platformCoin: "PLATFORMCOIN",
    bcl: "BCL",
    ...getDimensions(),
    siteName: "SITENAME",
    host: "HOST",
    mascot: "MASCOT",
    isLowEndDevice: false,
    initSearchParams: !isServer ? new URLSearchParams(location.search) : undefined,
    initHashParams: !isServer ? location.hash : undefined,
    langs: {},
    mobile: !isServer
      ? (viewport[0] < window.screen.width / 2 && viewport[0] < MOBILE_BREAKPOINT) ||
        (viewport.every(v => v <= 1000) && viewport.some(v => v <= MOBILE_BREAKPOINT))
      : false,
    localCurrencyConfig: [],
    get serverTime(): Date {
      return new Date(Date.now() - state.timeDiff);
    },
    inActive: false,
    appName: "BC.GAME",
    isPWA: false,
    isAPP: false
  });

  !isServer &&
    globalThis.addEventListener(
      "resize",
      throttle(() => setState(getDimensions()), 100)
    );

  if (isAndroid()) {
    import("detect-gpu").then(gpu => {
      gpu.getGPUTier().then((result: { type: string; tier: number }) => {
        if (result.type === "BENCHMARK") {
          setState("isLowEndDevice", result.tier <= 2);
        }
      });
    });
  }

  return [state, setState] as const;
});

export { env, setEnv };
