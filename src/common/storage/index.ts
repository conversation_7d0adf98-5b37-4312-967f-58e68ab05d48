import { createComputed } from "solid-js";
import { createStore, SetStoreFunction, Store } from "solid-js/store";
import { isServer } from "solid-js/web";
import murmurHash from "murmurhash";

type PersistOptions<T> = {
  name: string;
  deserialize?: (value: string) => Partial<T>;
};

export function createPersistedStore<T extends Record<string, any>>(
  initialState: T,
  options: PersistOptions<T>
): [Store<T>, SetStoreFunction<T>] {
  const versionHash = murmurHash.v3(JSON.stringify(initialState)).toString(36);
  const storage = !isServer ? window.localStorage : null;
  const localStorageKey = options.name;

  function loadFromStorage(): Partial<T> {
    let raw = storage?.getItem(localStorageKey);
    if (raw && !raw.includes(versionHash)) {
      raw = null;
    }
    try {
      return raw ? (options.deserialize || JSON.parse)(raw) : {};
    } catch {
      return {};
    }
  }

  const restoredState = loadFromStorage();
  const store = createStore<T>({
    ...initialState,
    __version: versionHash,
    ...restoredState
  });

  createComputed(() => {
    storage?.setItem(localStorageKey, JSON.stringify(store));
  });

  return store;
}
