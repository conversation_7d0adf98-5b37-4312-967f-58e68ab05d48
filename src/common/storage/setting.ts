import { createComputed, createEffect, createRoot } from "solid-js";
import { createPersistedStore } from ".";
import { getAccountSetting, preAccountReq, remoteSetting } from "@/services/account";
import { getValidCurrency } from "./wallet";
import { getHostType } from "../utils";
import { env } from "./env";
import { AccountSetting } from "@/types/account";
import { preWalletReq } from "@/services/wallet";

const [setting, setSetting] = createRoot(() => {
  const [store, setStore] = createPersistedStore(
    {
      lang: "en",
      darken: true,
      theme: "dark",
      firstVisit: true,
      preVisitTime: 0
    },
    { name: "setting" }
  );

  createComputed(() => setStore("darken", store.theme !== "light"));

  if (store.preVisitTime === 0) {
    setStore("preVisitTime", Date.now());
  } else {
    setStore("firstVisit", false);
  }

  createEffect(() => {
    // const socket = getSocket();
    // const prevQuery = socket.opts.query;
    // socket.opts.query = {
    //   ...prevQuery,
    //   "Accept-Language": store.lang,
    // };
  });

  return [store, setStore];
});

async function getSetting() {
  await new Promise(resolve => setTimeout(resolve, 1000));
  const account = await preAccountReq;
  const isLoggedIn = account.login;

  async function resolveCurrencySetting() {
    const wallet = await preWalletReq;
    return getValidCurrency(wallet, account) as AccountSetting;
  }

  if (isLoggedIn) {
    const accountSetting = await getAccountSetting();
    if (accountSetting)
      try {
        remoteSetting.value = JSON.stringify(accountSetting);
        const latestAccount = await preAccountReq;
        const wallet = await preWalletReq;
        let currencyName = accountSetting.currencyName;
        wallet.find(item => item.currencyName === currencyName) ||
          (currencyName = getValidCurrency(wallet, latestAccount).currencyName),
          (accountSetting.currencyName = currencyName);
        const hostInfo = getHostInfo();
        if (hostInfo) {
          accountSetting.localeCurrencyName = hostInfo;
          accountSetting.enableLocaleCurrency = !0;
        }
        return accountSetting;
      } catch {
        return resolveCurrencySetting();
      }
    else {
      return resolveCurrencySetting();
    }
  } else {
    return resolveCurrencySetting();
  }
}

function getHostInfo() {
  const {
    isNgHost: isNgHost,
    isNg2Host: isNg2Host,
    isIdHost: isIdHost,
    isKenyaHost: isKenyaHost,
    isBrAuditHost: isBrAuditHost,
    isBrHost: isBrHost,
    isMxHost: isMxHost
  } = getHostType(env.host);
  let fiat = "";
  if (isNgHost || isNg2Host) {
    fiat = "NGNFIAT";
  }
  if (isIdHost) {
    fiat = "INRFIAT";
  }
  if (isKenyaHost) {
    fiat = "KESFIAT";
  }
  if (isBrAuditHost || isBrHost) {
    fiat = "BRLFIAT";
  }
  if (isMxHost) {
    fiat = "MXNFIAT";
  }

  return fiat;
}

export { setting, setSetting, getSetting };
