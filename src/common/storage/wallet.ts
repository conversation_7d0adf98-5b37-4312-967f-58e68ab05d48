import { Wallet } from "@/types/wallet";
import { env } from "./env";
import { Account, AccountSetting } from "@/types/account";

export function getValidCurrency(wallet: Wallet, account: Account) {
  const bonusCurrencyName = account.bonusCurrencyName;
  const walletByCurrency = wallet.reduce<Record<string, Wallet[0]>>((map, wallet) => {
    map[wallet.currencyName] = wallet;
    return map;
  }, {});

  let enableLocaleCurrency = false;
  let selectedCurrencyName = wallet.sort((a, b) => Number(b.amount.sub(a.amount)))[0].currencyName;

  let localeCurrencyName: string = "USDFIAT";
  const matchedLocalCurrencyConfig = env.localCurrencyConfig.find(
    config => config[2] === account.areaCode
  );

  if (bonusCurrencyName === "BCD" && walletByCurrency.USDT && walletByCurrency.USDT.display) {
    selectedCurrencyName = "USDT";
  } else {
    if (walletByCurrency[bonusCurrencyName]) {
      selectedCurrencyName = bonusCurrencyName;
    }
    if (bonusCurrencyName.endsWith("FIAT")) {
      localeCurrencyName = bonusCurrencyName;
      enableLocaleCurrency = true;
    } else {
      if (matchedLocalCurrencyConfig) {
        localeCurrencyName = matchedLocalCurrencyConfig[0] + "FIAT";
        enableLocaleCurrency = true;
        if (!walletByCurrency[bonusCurrencyName] && walletByCurrency[localeCurrencyName]) {
          selectedCurrencyName = localeCurrencyName;
        }
      }
    }
  }

  const fallbackFiatCurrency = wallet.find(
    wallet => wallet.display && wallet.currencyName.endsWith("FIAT")
  );
  if (fallbackFiatCurrency) {
    selectedCurrencyName = fallbackFiatCurrency.currencyName;
    localeCurrencyName = selectedCurrencyName;
    enableLocaleCurrency = !0;
  }
  return {
    currencyName: selectedCurrencyName,
    localeCurrencyName: localeCurrencyName,
    enableLocaleCurrency: enableLocaleCurrency
  };
}
