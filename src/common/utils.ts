import { isServer } from "solid-js/web";
import { env } from "./storage/env";

export function memo<T extends (...args: any[]) => any>(originalFunction: T): T {
  // 缓存存储，键为参数字符串，值为函数返回结果
  const cache = new Map<string, ReturnType<T>>();

  // 返回记忆化后的函数
  return function (...args: Parameters<T>): ReturnType<T> {
    // 将参数数组转换为字符串作为缓存键
    const argsKey = args.toString();

    // 如果缓存中存在该键，直接返回缓存的结果
    if (cache.has(argsKey)) {
      return cache.get(argsKey)!;
    }

    // 调用原始函数计算结果
    const result = originalFunction(...args);

    // 将结果存入缓存并返回
    cache.set(argsKey, result);
    return result;
  } as T;
}

export const getHostType = memo((hostname: string) => {
  const brHosts = ["bc.me", "bc.ai", "bcga.me"],
    kenyaHosts = ["bcgame.ke"],
    spreadHosts = ["bc.xyz"],
    skHosts = ["bcgame.sk"],
    usHosts = ["bcgame.us"],
    ngHosts = ["bcbet.ng"],
    ruHosts = ["bcigra.com", "bcfeast88.com", "bcwildwagers.com"],
    spHosts = ["87.com"],
    ng2Hosts = ["bcgamebet.ng"],
    idHosts = ["bcgame.vc"],
    brAuditHosts = ["bcsports.game", "dogcrash.xyz"],
    anjouanHosts = ["bckm.top"],
    sportsHosts = ["bcgame.ke", "bcbet.ng"],
    bzHosts = ["bzjogos.com", "cocoforyou.top", "bcsports.game"],
    mxHosts = ["bcgame.mx"];

  return {
    isBrHost: brHosts.includes(hostname),
    isKenyaHost: kenyaHosts.includes(hostname),
    isSpreadHost: spreadHosts.includes(hostname),
    isUsHost: usHosts.includes(hostname),
    isSkHost: skHosts.includes(hostname),
    isNgHost: ngHosts.includes(hostname),
    isRuHosts: ruHosts.includes(hostname),
    isSpHost: spHosts.includes(hostname),
    isNg2Host: ng2Hosts.includes(hostname),
    isIdHost: idHosts.includes(hostname),
    isAnjouanHost: anjouanHosts.includes(hostname),
    isBrAuditHost: brAuditHosts.includes(hostname),
    isSportsHost: sportsHosts.includes(hostname),
    isBzHost: bzHosts.includes(hostname),
    isMxHost: mxHosts.includes(hostname)
  };
});

export function isAndroid() {
  return !isServer ? /android/i.test(navigator.userAgent) : false;
}

export const swipeArea = !isServer ? Math.ceil(innerWidth * 0.07) : 0;
export const isBot = !isServer ? navigator.webdriver : false;
export const shouldReduceAnimation = () => env.isLowEndDevice;
export const isSupportTouch = !isServer
  ? Object.prototype.hasOwnProperty.call(window, "ontouchstart")
  : false;

export const easeBack = [0.36, 0.66, 0.04, 1];
export function isIos() {
  return (
    ["iPad Simulator", "iPhone Simulator", "iPod Simulator", "iPad", "iPhone", "iPod"].includes(
      navigator.platform
    ) ||
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
}
