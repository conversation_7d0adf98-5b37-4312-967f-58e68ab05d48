import { createResource, Show } from "solid-js";

// 使用简化的类型定义，避免与原有类型冲突
interface MockAccount {
  login: boolean;
  username: string;
  email: string;
  chatRoomPermission?: {
    canSend: boolean;
    canReceive: boolean;
  };
}

interface MockWallet {
  balance: number;
  currency: string;
  frozen: number;
  available: number;
}

// 模拟 API 调用函数
const getMockAccount = async (): Promise<MockAccount> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    login: true,
    username: "testuser",
    email: "<EMAIL>",
    chatRoomPermission: {
      canSend: true,
      canReceive: true
    }
  };
};

const getMockWallet = async (): Promise<MockWallet> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  return {
    balance: 1000.5,
    currency: "USD",
    frozen: 0,
    available: 1000.5
  };
};

export default function AccountInfo() {
  const [account] = createResource(getMockAccount);
  const [wallet] = createResource(getMockWallet);

  return (
    <div class="account-info">
      <h2>Account Information</h2>

      <Show when={account.loading}>
        <div data-testid="account-loading">Loading account...</div>
      </Show>

      <Show when={account.error}>
        <div data-testid="account-error" class="error">
          Error loading account: {account.error?.message}
        </div>
      </Show>

      <Show when={account()}>
        <div data-testid="account-data">
          <h3>User: {account()?.username}</h3>
          <p>Email: {account()?.email}</p>
          <p>Status: {account()?.login ? "Logged in" : "Not logged in"}</p>
        </div>
      </Show>

      <Show when={wallet.loading}>
        <div data-testid="wallet-loading">Loading wallet...</div>
      </Show>

      <Show when={wallet.error}>
        <div data-testid="wallet-error" class="error">
          Error loading wallet: {wallet.error?.message}
        </div>
      </Show>

      <Show when={wallet()}>
        <div data-testid="wallet-data">
          <h3>Wallet</h3>
          <p>
            Balance: {wallet()?.balance} {wallet()?.currency}
          </p>
          <p>
            Available: {wallet()?.available} {wallet()?.currency}
          </p>
          <p>
            Frozen: {wallet()?.frozen} {wallet()?.currency}
          </p>
        </div>
      </Show>
    </div>
  );
}
