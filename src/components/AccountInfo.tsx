import { createSignal, createResource, Show } from 'solid-js';
import { getAccount } from '@/services/account';
import { getWallet } from '@/services/wallet';

export default function AccountInfo() {
  const [account] = createResource(getAccount);
  const [wallet] = createResource(getWallet);

  return (
    <div class="account-info">
      <h2>Account Information</h2>
      
      <Show when={account.loading}>
        <div data-testid="account-loading">Loading account...</div>
      </Show>
      
      <Show when={account.error}>
        <div data-testid="account-error" class="error">
          Error loading account: {account.error?.message}
        </div>
      </Show>
      
      <Show when={account()}>
        <div data-testid="account-data">
          <h3>User: {account()?.username}</h3>
          <p>Email: {account()?.email}</p>
          <p>Status: {account()?.login ? 'Logged in' : 'Not logged in'}</p>
        </div>
      </Show>

      <Show when={wallet.loading}>
        <div data-testid="wallet-loading">Loading wallet...</div>
      </Show>
      
      <Show when={wallet.error}>
        <div data-testid="wallet-error" class="error">
          Error loading wallet: {wallet.error?.message}
        </div>
      </Show>
      
      <Show when={wallet()}>
        <div data-testid="wallet-data">
          <h3>Wallet</h3>
          <p>Balance: {wallet()?.balance} {wallet()?.currency}</p>
          <p>Available: {wallet()?.available} {wallet()?.currency}</p>
          <p>Frozen: {wallet()?.frozen} {wallet()?.currency}</p>
        </div>
      </Show>
    </div>
  );
}
