import { createResource, Show } from "solid-js";
import { mockAccount, mockAccountSetting, mockWallet } from "@/mocks/handlers";

// 模拟 API 调用函数（在实际项目中这些会是真正的 API 调用）
const fetchAccountData = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockAccount;
};

const fetchAccountSettings = async () => {
  await new Promise(resolve => setTimeout(resolve, 300));
  return mockAccountSetting;
};

const fetchWalletData = async () => {
  await new Promise(resolve => setTimeout(resolve, 400));
  return mockWallet;
};

export default function MSWDemo() {
  const [account] = createResource(fetchAccountData);
  const [settings] = createResource(fetchAccountSettings);
  const [wallet] = createResource(fetchWalletData);

  return (
    <div class="p-6 max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6 text-center">MSW 集成演示</h1>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 账户信息卡片 */}
        <div class="bg-white rounded-lg shadow-md p-6 border">
          <h2 class="text-xl font-semibold mb-4 text-blue-600">账户信息</h2>

          <Show when={account.loading}>
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
          </Show>

          <Show when={account.error}>
            <div class="text-red-500">加载失败: {account.error?.message}</div>
          </Show>

          <Show when={account()}>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">用户名:</span>
                <span class="font-medium">{account()?.username}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">邮箱:</span>
                <span class="font-medium">{account()?.email}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">状态:</span>
                <span class={`font-medium ${account()?.login ? "text-green-600" : "text-red-600"}`}>
                  {account()?.login ? "已登录" : "未登录"}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">聊天权限:</span>
                <span class="font-medium">
                  {account()?.chatRoomPermission?.canSend ? "✅" : "❌"}
                </span>
              </div>
            </div>
          </Show>
        </div>

        {/* 设置信息卡片 */}
        <div class="bg-white rounded-lg shadow-md p-6 border">
          <h2 class="text-xl font-semibold mb-4 text-green-600">用户设置</h2>

          <Show when={settings.loading}>
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
          </Show>

          <Show when={settings.error}>
            <div class="text-red-500">加载失败: {settings.error?.message}</div>
          </Show>

          <Show when={settings()}>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">主题:</span>
                <span class="font-medium capitalize">{settings()?.theme}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">语言:</span>
                <span class="font-medium uppercase">{settings()?.language}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">通知:</span>
                <span
                  class={`font-medium ${settings()?.notifications ? "text-green-600" : "text-red-600"}`}
                >
                  {settings()?.notifications ? "开启" : "关闭"}
                </span>
              </div>
            </div>
          </Show>
        </div>

        {/* 钱包信息卡片 */}
        <div class="bg-white rounded-lg shadow-md p-6 border">
          <h2 class="text-xl font-semibold mb-4 text-purple-600">钱包信息</h2>

          <Show when={wallet.loading}>
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
          </Show>

          <Show when={wallet.error}>
            <div class="text-red-500">加载失败: {wallet.error?.message}</div>
          </Show>

          <Show when={wallet()}>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">总余额:</span>
                <span class="font-bold text-lg">
                  {wallet()?.balance} {wallet()?.currency}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">可用:</span>
                <span class="font-medium text-green-600">
                  {wallet()?.available} {wallet()?.currency}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">冻结:</span>
                <span class="font-medium text-red-600">
                  {wallet()?.frozen} {wallet()?.currency}
                </span>
              </div>
            </div>
          </Show>
        </div>
      </div>

      {/* MSW 状态信息 */}
      <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">MSW 集成状态</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-green-600 font-medium">✅ MSW 框架已集成</span>
            <p class="text-gray-600">版本: 2.10.2</p>
          </div>
          <div>
            <span class="text-green-600 font-medium">✅ Mock 数据已配置</span>
            <p class="text-gray-600">6 个 API 处理器</p>
          </div>
          <div>
            <span class="text-green-600 font-medium">✅ 测试环境就绪</span>
            <p class="text-gray-600">支持单元测试和集成测试</p>
          </div>
          <div>
            <span class="text-blue-600 font-medium">ℹ️ 开发环境演示</span>
            <p class="text-gray-600">当前显示的是模拟数据</p>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div class="mt-6 bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-2 text-blue-800">如何使用</h3>
        <ul class="text-sm text-blue-700 space-y-1">
          <li>• 在测试中：MSW 会自动拦截 API 请求并返回模拟数据</li>
          <li>• 在开发中：可以启用 MSW 来模拟后端 API</li>
          <li>
            • 查看 <code class="bg-blue-100 px-1 rounded">docs/MSW_GUIDE.md</code> 获取详细文档
          </li>
          <li>
            • 运行 <code class="bg-blue-100 px-1 rounded">npm test</code> 查看测试示例
          </li>
        </ul>
      </div>
    </div>
  );
}
