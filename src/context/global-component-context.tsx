import { createContext, useContext } from "solid-js";

export type GlobalComponents = {
  GameRecommend: {
    (params: any): any;
    preload: () => any;
  };
  gameTabs: () => any;
  selectCurrency: () => any;
};
export const GlobalComponentsCtx = createContext<GlobalComponents>({} as GlobalComponents);

export function useGlobalComponent<T extends keyof GlobalComponents>(L: T): GlobalComponents[T] {
  return useContext(GlobalComponentsCtx)[L] || (() => null);
}
