import { routerUtils } from "@/common/router";
import {
  NavigateOptions,
  useCurrentMatches,
  useLocation,
  useNavigate as useSolidjsNavigate
} from "@solidjs/router";
import {
  Accessor,
  createComponent,
  createComputed,
  createContext,
  createMemo,
  createSignal,
  getOwner,
  JSX,
  onCleanup,
  useContext
} from "solid-js";
import { RouterOwner } from "./router-owner-context";

interface NavigatorContextProps {
  navigate: (to: number | string, options?: Partial<NavigateOptions>) => void;
  action: Accessor<string>;
  referrer: Accessor<string>;
}

export const NavigatorContext = createContext<NavigatorContextProps | null>(null);

interface NavigatorProviderProps {
  children: JSX.Element;
  base: string;
}

type NavigationState = [string, number, any[]];

let globalAction: string | undefined;
let isSwipeBack = false;

export function NavigatorProvider(props: NavigatorProviderProps) {
  const navigate = useSolidjsNavigate();
  const location = useLocation();
  const owner = getOwner();
  const [referrer, setReferrer] = createSignal("/");
  createComputed(() => {
    const pathname = location.pathname;
    onCleanup(() => {
      setReferrer(pathname);
    });
  });
  const matches = useCurrentMatches();
  createComputed(() => {
    location.pathname.startsWith(props.base) || window.location.reload();
  });
  const navigationState = createMemo<NavigationState>(
      ([, depth]: [string, number, any[]]) => {
        var historyState;
        const currentMatches = matches();
        const currentDepth =
          ((historyState = history.state) == null ? undefined : historyState._depth) || 0;
        let result: NavigationState;

        if (globalAction !== undefined) {
          result = [globalAction, depth + (globalAction === "PUSH" ? 1 : 0), currentMatches];
        } else {
          result = [
            depth > currentDepth ? "POP" : depth === currentDepth ? "REPLACE" : "PUSH",
            currentDepth,
            currentMatches
          ];
        }
        globalAction = undefined;
        return result;
      },
      ["PUSH", 0, []]
    ),
    handleNavigate = (to: number | string, options?: Partial<NavigateOptions>) => {
      let historyState;
      isSwipeBack = !1;
      if (typeof to == "number") {
        if (to <= -99) {
          to = Math.min(
            (((historyState = history.state) == null ? undefined : historyState._depth) || 0) -
              history.length,
            -1
          );
        }
        return navigate(to.toString(), {
          scroll: !1
        });
      }

      globalAction = options?.replace ? "REPLACE" : "PUSH";
      if (options?.state) {
        options.state = JSON.parse(JSON.stringify(options.state));
      }
      const { urlOriginal: url } = routerUtils.extractLocale(to);
      return navigate(url, {
        scroll: !1,
        ...options
      });
    };

  return (
    <RouterOwner.Provider value={owner}>
      <NavigatorContext.Provider
        value={{ navigate: handleNavigate, action: () => navigationState()[0] as string, referrer }}
      >
        {props.children}
      </NavigatorContext.Provider>
    </RouterOwner.Provider>
  );
}

export const useNavigate = () => useContext(NavigatorContext)?.navigate;
export const useReferrer = () => useContext(NavigatorContext)?.referrer;
