// @refresh reload
import { mount, StartClient } from "@solidjs/start/client";
import { onMount } from "solid-js";

// 在开发环境启用 MSW
async function enableMocking() {
  if (import.meta.env.DEV) {
    const { enableMocking } = await import("./mocks/browser");
    return enableMocking();
  }
}

enableMocking().then(() => {
  mount(() => <StartClient />, document.getElementById("root")!);
  onMount(() => document.getElementById("start-up")?.remove());
});
