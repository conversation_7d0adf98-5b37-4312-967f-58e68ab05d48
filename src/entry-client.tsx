// @refresh reload
import { mount, StartClient } from "@solidjs/start/client";
import { onMount } from "solid-js";

// 在开发环境启用 MSW
async function enableMocking() {
  console.log("🚀 应用启动: 检查环境...");
  console.log("📍 开发环境:", import.meta.env.DEV);
  console.log("🌐 浏览器环境:", typeof window !== "undefined");

  if (import.meta.env.DEV) {
    console.log("🔄 加载 MSW 模块...");
    try {
      const { enableMocking } = await import("./mocks/browser");
      console.log("✅ MSW 模块加载成功");
      return enableMocking();
    } catch (error) {
      console.error("❌ MSW 模块加载失败:", error);
    }
  } else {
    console.log("⚠️ 生产环境，跳过 MSW");
  }
}

console.log("🎯 开始启动应用...");
enableMocking()
  .then(() => {
    console.log("🎉 MSW 初始化完成，挂载应用");
    mount(() => <StartClient />, document.getElementById("root")!);
    onMount(() => document.getElementById("start-up")?.remove());
  })
  .catch(error => {
    console.error("💥 应用启动失败:", error);
    // 即使 MSW 失败也要启动应用
    mount(() => <StartClient />, document.getElementById("root")!);
    onMount(() => document.getElementById("start-up")?.remove());
  });
