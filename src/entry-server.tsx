// @refresh reload
import { create<PERSON><PERSON><PERSON>, StartServer } from "@solidjs/start/server";

export default createHandler(() => (
  <StartServer
    document={({ assets, children, scripts }) => (
      <html lang="en">
        <head>
          <meta charset="utf-8" />
          <meta
            name="viewport"
            content="width=device-width,minimum-scale=1,maximum-scale=1,user-scalable=no,initial-scale=1,viewport-fit=cover"
          />
          <meta name="theme-color" content="#232626" />
          <meta name="apple-mobile-web-app-capable" content="yes" />
          <meta name="mobile-web-app-capable" content="yes" />
          <meta
            name="apple-mobile-web-app-status-bar-style"
            content="black-translucent"
          />
          <meta name="format-detection" content="telephone=no,email=no" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <meta http-equiv="x-ua-compatible" content="IE=edge" />
          <link
            rel="apple-touch-icon"
            sizes="180x180"
            href="/apple-touch-icon.png"
          />
          <link rel="manifest" href="/manifest.webmanifest" />
          <link
            rel="preconnect"
            href="https://bc.imgix.net"
            crossorigin="anonymous"
          />
          <link rel="dns-prefetch" href="https://bc.imgix.net" />
          <style>
            {`
              body {
                background-color: #f4f4f4;
              }

              #start-up {
                width: 11rem;
                height: 11rem;
                background-image: url(/assets/init/init_w.png);
                background-size: contain;
                position: fixed;
                left: 50%;
                top: 50%;
                margin: -5.5rem 0 0 -5.5rem;
              }

              .dark #start-up {
                background-image: url(/assets/init/init.png);
              }

              .dark body {
                background-color: #232626;
              }

              #start-up.festival {
                background-image: url(/assets/init/init_w_festival.png);
              }

              .dark #start-up.festival {
                background-image: url(/assets/init/init_festival.png);
              }
          `}
          </style>
          <script>
            const setting = localStorage.getItem('setting');
            document.documentElement.classList.toggle('dark', setting ?
            JSON.parse(setting).darken : false);
          </script>
          <script>
            window.OneSignalDeferred = window.OneSignalDeferred || [];
          </script>
          {assets}
        </head>
        <body>
          <noscript>You need to enable JavaScript to run this app.</noscript>
          <div id="root">{children}</div>
          <div id="start-up"></div>
          {scripts}
        </body>
      </html>
    )}
  />
));
