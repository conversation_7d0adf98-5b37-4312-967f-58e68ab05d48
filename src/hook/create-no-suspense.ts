import { Accessor, createEffect, createSignal } from "solid-js";

export function createNoSuspense<T>(source: Accessor<T>, initialValue: T = source()): Accessor<T> {
  const [signal, setSignal] = createSignal<T>(initialValue);
  return (
    createEffect(() => {
      const value = source();
      if (value !== undefined && value !== null) {
        setSignal(value);
      }
    }),
    signal
  );
}
