import { account } from "@/common/storage/account";
import { useNavigate } from "@solidjs/router";

export function useLoginCallback<T extends (...args: any[]) => any>(
  callback: T,
  redirectUrl: string = "#/login"
): (...args: Parameters<T>) => ReturnType<T> | void {
  return (...args: Parameters<T>) => {
    if (!account.login) {
      useNavigate()(redirectUrl);
      return;
    }
    return callback(...args);
  };
}
