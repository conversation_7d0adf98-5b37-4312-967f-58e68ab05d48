import { setupWorker } from "msw/browser";
import { handlers } from "./handlers";

// 为浏览器环境设置 MSW worker
export const worker = setupWorker(...handlers);

// 启动 worker 的辅助函数
export async function enableMocking() {
  if (typeof window === "undefined") {
    console.log("❌ MSW: 服务器环境，跳过启动");
    return;
  }

  console.log("🔄 MSW: 开始启动...");
  console.log("📋 MSW: 已注册", handlers.length, "个处理器");

  try {
    await worker.start({
      onUnhandledRequest: "warn",
      serviceWorker: {
        url: "/mockServiceWorker.js"
      }
    });

    console.log("✅ MSW: 启动成功！");
    console.log("🔶 MSW enabled for development");

    // 列出所有处理器
    handlers.forEach((handler, index) => {
      console.log(`  ${index + 1}. ${handler.info?.header || "Unknown handler"}`);
    });
  } catch (error) {
    console.error("❌ MSW: 启动失败", error);
  }
}
