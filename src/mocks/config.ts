// MSW 配置管理

export interface MSWConfig {
  enabled: boolean;
  modules: {
    account: boolean;
    wallet: boolean;
  };
}

// 默认配置
const defaultConfig: MSWConfig = {
  enabled: false, // 默认关闭，避免影响正常开发
  modules: {
    account: true,
    wallet: true,
  }
};

// 从环境变量或 localStorage 读取配置
function loadConfig(): MSWConfig {
  // 优先从环境变量读取
  const envEnabled = import.meta.env.VITE_MSW_ENABLED === 'true';
  const envAccountEnabled = import.meta.env.VITE_MSW_ACCOUNT !== 'false';
  const envWalletEnabled = import.meta.env.VITE_MSW_WALLET !== 'false';

  // 从 localStorage 读取（浏览器环境）
  let localConfig = defaultConfig;
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem('msw-config');
      if (stored) {
        localConfig = { ...defaultConfig, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Failed to load MSW config from localStorage:', error);
    }
  }

  return {
    enabled: envEnabled || localConfig.enabled,
    modules: {
      account: envAccountEnabled && localConfig.modules.account,
      wallet: envWalletEnabled && localConfig.modules.wallet,
    }
  };
}

// 保存配置到 localStorage
function saveConfig(config: MSWConfig): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('msw-config', JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save MSW config to localStorage:', error);
    }
  }
}

// 当前配置
let currentConfig = loadConfig();

// 配置管理器
export const mswConfig = {
  get: () => currentConfig,
  
  set: (config: Partial<MSWConfig>) => {
    currentConfig = { ...currentConfig, ...config };
    saveConfig(currentConfig);
    console.log('🔧 MSW 配置已更新:', currentConfig);
  },
  
  toggle: () => {
    currentConfig.enabled = !currentConfig.enabled;
    saveConfig(currentConfig);
    console.log(`🔄 MSW ${currentConfig.enabled ? '已启用' : '已禁用'}`);
    return currentConfig.enabled;
  },
  
  toggleModule: (module: keyof MSWConfig['modules']) => {
    currentConfig.modules[module] = !currentConfig.modules[module];
    saveConfig(currentConfig);
    console.log(`🔄 MSW ${module} 模块 ${currentConfig.modules[module] ? '已启用' : '已禁用'}`);
    return currentConfig.modules[module];
  },
  
  reset: () => {
    currentConfig = { ...defaultConfig };
    saveConfig(currentConfig);
    console.log('🔄 MSW 配置已重置');
  }
};

// 在控制台暴露配置管理器（开发时使用）
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  (window as any).mswConfig = mswConfig;
  console.log('💡 MSW 配置管理器已暴露到 window.mswConfig');
  console.log('💡 使用方法:');
  console.log('  - window.mswConfig.toggle() // 切换 MSW 开关');
  console.log('  - window.mswConfig.toggleModule("account") // 切换账户模块');
  console.log('  - window.mswConfig.toggleModule("wallet") // 切换钱包模块');
}
