import { http, HttpResponse } from 'msw';
import { Account, AccountSetting } from '@/types/account';
import { Wallet } from '@/types/wallet';

// Mock 数据
const mockAccount: Account = {
  login: true,
  id: '12345',
  username: 'testuser',
  email: '<EMAIL>',
  chatRoomPermission: {
    canSend: true,
    canReceive: true
  }
};

const mockAccountSetting: AccountSetting = {
  theme: 'dark',
  language: 'en',
  notifications: true
};

const mockWallet: Wallet = {
  balance: 1000.50,
  currency: 'USD',
  frozen: 0,
  available: 1000.50
};

// API 响应格式
const createApiResponse = <T>(data: T, code = 0, msg = 'success') => ({
  code,
  data,
  msg
});

// MSW 处理器
export const handlers = [
  // 获取账户信息
  http.get('/api/account/get/', () => {
    return HttpResponse.json(createApiResponse(mockAccount));
  }),

  // 获取账户设置
  http.get('/api/account/setting/ns_user/get/', () => {
    return HttpResponse.json(createApiResponse(mockAccountSetting));
  }),

  // 保存账户设置
  http.post('/api/account/setting/ns_user/reset/', async ({ request }) => {
    const body = await request.json();
    console.log('Saving account settings:', body);
    return HttpResponse.json(createApiResponse({ success: true }));
  }),

  // 获取钱包信息
  http.get('/api/user/amount/', () => {
    return HttpResponse.json(createApiResponse(mockWallet));
  }),

  // 错误场景示例 - 未授权访问
  http.get('/api/account/unauthorized/', () => {
    return HttpResponse.json(
      createApiResponse(null, 403, 'Unauthorized access'),
      { status: 403 }
    );
  }),

  // 网络错误场景示例
  http.get('/api/network/error/', () => {
    return HttpResponse.error();
  })
];

// 导出 mock 数据供测试使用
export { mockAccount, mockAccountSetting, mockWallet };
