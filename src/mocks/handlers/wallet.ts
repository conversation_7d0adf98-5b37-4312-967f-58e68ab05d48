import { http, HttpResponse } from 'msw';
import { Wallet, AmountItem } from '@/types/wallet';
import { mswConfig } from '../config';
import Decimal from 'decimal.js';

// 基于真实类型的 Mock 钱包数据
const createMockAmountItem = (
  currencyName: string,
  amount: string,
  sort: number,
  options: Partial<AmountItem> = {}
): AmountItem => ({
  lastLogId: `mock_log_${Date.now()}_${currencyName}`,
  currencyName,
  aliasCurrencyName: currencyName,
  amount: new Decimal(amount),
  generalAmount: new Decimal(amount),
  bonusAmount: new Decimal('0'),
  groupAmount: {
    free_amount: new Decimal(amount),
    sports: '0'
  },
  abnormal: false,
  sort,
  display: true,
  displayStatus: 1,
  supportTx: true,
  useable: true,
  areaAble: true,
  ...options
});

const mockWallet: Wallet = [
  createMockAmountItem('BTC', '0.05432100', 1),
  createMockAmountItem('ETH', '1.23456789', 2),
  createMockAmountItem('USDT', '1000.50000000', 3),
  createMockAmountItem('BCD', '500.00000000', 4, { 
    bonusAmount: new Decimal('100.00000000') 
  }),
  createMockAmountItem('TRX', '10000.123456', 5),
  createMockAmountItem('DOGE', '5000.12345678', 6),
  createMockAmountItem('LTC', '2.87654321', 7),
  createMockAmountItem('XRP', '800.123456', 8),
  createMockAmountItem('ADA', '1500.123456', 9),
  createMockAmountItem('DOT', '50.123456', 10)
];

// 钱包相关的 MSW 处理器
export const walletHandlers = [
  // 获取钱包信息
  http.get('/user/amount/', ({ request }) => {
    const config = mswConfig.get();
    if (!config.enabled || !config.modules.wallet) {
      // 如果 MSW 未启用或钱包模块未启用，则放行请求
      return;
    }

    console.log('🎭 MSW: 拦截钱包信息请求');
    
    const url = new URL(request.url);
    const mockType = url.searchParams.get('mock');
    const currency = url.searchParams.get('currency');
    
    let responseData = [...mockWallet];
    
    // 支持不同的 mock 场景
    switch (mockType) {
      case 'empty':
        // 空钱包场景
        responseData = responseData.map(item => ({
          ...item,
          amount: new Decimal('0'),
          generalAmount: new Decimal('0'),
          bonusAmount: new Decimal('0'),
          groupAmount: {
            ...item.groupAmount,
            free_amount: new Decimal('0')
          }
        }));
        break;
        
      case 'rich':
        // 富豪钱包场景
        responseData = responseData.map(item => ({
          ...item,
          amount: new Decimal(item.amount.toString()).mul(100),
          generalAmount: new Decimal(item.generalAmount.toString()).mul(100),
          groupAmount: {
            ...item.groupAmount,
            free_amount: new Decimal(item.groupAmount.free_amount.toString()).mul(100)
          }
        }));
        break;
        
      case 'single':
        // 只显示指定货币
        if (currency) {
          responseData = responseData.filter(item => 
            item.currencyName.toLowerCase() === currency.toLowerCase()
          );
        }
        break;
        
      case 'abnormal':
        // 异常状态
        responseData = responseData.map(item => ({
          ...item,
          abnormal: true,
          useable: false
        }));
        break;
    }
    
    // 过滤不显示的货币（模拟真实业务逻辑）
    const filteredData = responseData.filter(item => item.display);

    return HttpResponse.json({
      code: 0,
      data: filteredData,
      msg: 'success'
    });
  }),

  // 获取特定货币余额
  http.get('/user/amount/:currency', ({ params, request }) => {
    const config = mswConfig.get();
    if (!config.enabled || !config.modules.wallet) {
      return;
    }

    const { currency } = params;
    console.log(`🎭 MSW: 拦截 ${currency} 余额请求`);
    
    const currencyData = mockWallet.find(item => 
      item.currencyName.toLowerCase() === (currency as string).toLowerCase()
    );

    if (!currencyData) {
      return HttpResponse.json({
        code: 404,
        data: null,
        msg: `Currency ${currency} not found`
      }, { status: 404 });
    }

    return HttpResponse.json({
      code: 0,
      data: currencyData,
      msg: 'success'
    });
  }),

  // 模拟钱包相关的错误场景
  http.get('/user/amount/error-test/', () => {
    const config = mswConfig.get();
    if (!config.enabled || !config.modules.wallet) {
      return;
    }

    console.log('🎭 MSW: 模拟钱包错误场景');
    
    return HttpResponse.json({
      code: 500,
      data: null,
      msg: 'Mock wallet service error for testing'
    }, { status: 500 });
  }),

  // 模拟网络超时
  http.get('/user/amount/timeout-test/', async () => {
    const config = mswConfig.get();
    if (!config.enabled || !config.modules.wallet) {
      return;
    }

    console.log('🎭 MSW: 模拟网络超时');
    
    // 模拟长时间等待
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    return HttpResponse.json({
      code: 0,
      data: mockWallet,
      msg: 'success'
    });
  })
];

// 导出 mock 数据供其他地方使用
export { mockWallet };
