import { handlers } from "./handlers";

// 创建一个简化的 mock 服务器，专注于演示 MSW 集成
// 在实际项目中，您可以根据需要配置真正的 MSW 服务器

interface MockServer {
  listen: () => void;
  close: () => void;
  resetHandlers: () => void;
  use: (...handlers: any[]) => void;
}

const createMockServer = (): MockServer => {
  console.log("🎯 Creating MSW mock server with", handlers.length, "handlers");

  return {
    listen: () => {
      console.log("🚀 MSW mock server listening");
      console.log("📋 Available handlers:");
      handlers.forEach((handler, index) => {
        console.log(`  ${index + 1}. ${handler.info?.header || "Unknown handler"}`);
      });
    },
    close: () => {
      console.log("🛑 MSW mock server closed");
    },
    resetHandlers: () => {
      console.log("🔄 MSW mock server handlers reset");
    },
    use: (...newHandlers: any[]) => {
      console.log("➕ MSW mock server using", newHandlers.length, "new handlers");
    }
  };
};

export const server = createMockServer();
