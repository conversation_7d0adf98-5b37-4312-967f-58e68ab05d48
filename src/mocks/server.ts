import { handlers } from "./handlers";

// 创建一个简单的 mock 服务器对象
const createMockServer = () => {
  return {
    listen: () => console.log('Mock server listening'),
    close: () => console.log('Mock server closed'),
    resetHandlers: () => console.log('Mock server handlers reset'),
    use: (...newHandlers: any[]) => {
      console.log('Mock server using new handlers:', newHandlers.length);
    }
  };
};

// 导出 mock 服务器
export const server = createMockServer();
