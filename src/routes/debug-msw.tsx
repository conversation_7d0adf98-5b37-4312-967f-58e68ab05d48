import { createSignal, onMount } from "solid-js";

export default function DebugMSW() {
  const [logs, setLogs] = createSignal<string[]>([]);
  const [testResults, setTestResults] = createSignal<any[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `${timestamp}: ${message}`]);
    console.log(`[MSW Debug] ${message}`);
  };

  const testAPI = async (endpoint: string) => {
    addLog(`🔄 测试 ${endpoint}`);
    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      const result = {
        endpoint,
        success: true,
        status: response.status,
        data: data
      };
      setTestResults(prev => [...prev, result]);
      addLog(`✅ ${endpoint} - 状态: ${response.status}`);
      return result;
    } catch (error) {
      const result = {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
      setTestResults(prev => [...prev, result]);
      addLog(`❌ ${endpoint} - 错误: ${result.error}`);
      return result;
    }
  };

  const runTests = async () => {
    setTestResults([]);
    setLogs([]);

    addLog("🚀 开始 MSW 调试测试");

    // 检查环境
    addLog(`📍 开发环境: ${import.meta.env.DEV}`);
    addLog(`🌐 浏览器环境: ${typeof window !== "undefined"}`);
    addLog(
      `🔧 Service Worker 支持: ${typeof navigator !== "undefined" && "serviceWorker" in navigator}`
    );

    // 检查 MSW Service Worker
    if (typeof navigator !== "undefined" && "serviceWorker" in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        addLog(`📋 Service Worker 注册数量: ${registrations.length}`);
        registrations.forEach((reg, index) => {
          addLog(`  ${index + 1}. ${reg.scope} - 状态: ${reg.active?.state || "inactive"}`);
        });
      } catch (error) {
        addLog(`❌ 无法获取 Service Worker 注册信息: ${error}`);
      }
    }

    // 测试 API 端点
    const endpoints = [
      "/api/account/get/",
      "/api/account/setting/ns_user/get/",
      "/api/user/amount/",
      "/api/account/unauthorized/",
      "/api/network/error/"
    ];

    for (const endpoint of endpoints) {
      await testAPI(endpoint);
      // 添加小延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    addLog("🎉 测试完成");
  };

  const checkMSWStatus = async () => {
    addLog("🔍 检查 MSW 状态...");

    // 尝试手动启动 MSW
    try {
      const { worker } = await import("../mocks/browser");
      addLog("✅ MSW worker 模块加载成功");

      // 检查 worker 状态
      addLog(`🔧 Worker 状态: ${worker ? "已创建" : "未创建"}`);

      // 尝试手动启动
      if (worker) {
        try {
          await worker.start({
            onUnhandledRequest: "warn",
            serviceWorker: {
              url: "/mockServiceWorker.js"
            }
          });
          addLog("✅ MSW worker 手动启动成功");
        } catch (error) {
          addLog(`❌ MSW worker 手动启动失败: ${error}`);
        }
      }
    } catch (error) {
      addLog(`❌ MSW worker 模块加载失败: ${error}`);
    }
  };

  onMount(() => {
    addLog("🎯 调试页面已加载");
  });

  return (
    <div class="p-6 max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">MSW 调试页面</h1>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 控制面板 */}
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">控制面板</h2>
          <div class="space-y-3">
            <button
              onClick={checkMSWStatus}
              class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              检查 MSW 状态
            </button>
            <button
              onClick={runTests}
              class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            >
              运行 API 测试
            </button>
            <button
              onClick={() => {
                setLogs([]);
                setTestResults([]);
              }}
              class="w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            >
              清除日志
            </button>
          </div>

          <div class="mt-6">
            <h3 class="font-semibold mb-2">环境信息</h3>
            <div class="text-sm space-y-1">
              <div>开发环境: {import.meta.env.DEV ? "✅" : "❌"}</div>
              <div>浏览器: {typeof window !== "undefined" ? "✅" : "❌"}</div>
              <div>
                Service Worker:{" "}
                {typeof navigator !== "undefined" && "serviceWorker" in navigator ? "✅" : "❌"}
              </div>
            </div>
          </div>
        </div>

        {/* 日志面板 */}
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">调试日志</h2>
          <div class="bg-gray-900 text-green-400 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
            {logs().length === 0 ? (
              <div class="text-gray-500">点击按钮开始调试...</div>
            ) : (
              logs().map((log, index) => (
                <div key={index} class="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 测试结果 */}
      {testResults().length > 0 && (
        <div class="mt-6 bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">测试结果</h2>
          <div class="space-y-4">
            {testResults().map((result, index) => (
              <div
                key={index}
                class={`border rounded p-4 ${
                  result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"
                }`}
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium">{result.endpoint}</span>
                  <span
                    class={`px-2 py-1 rounded text-sm ${
                      result.success ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                    }`}
                  >
                    {result.success ? `状态: ${result.status}` : "失败"}
                  </span>
                </div>
                <pre class="text-sm bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(result.data || result.error, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 说明 */}
      <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="font-semibold mb-2 text-yellow-800">调试说明</h3>
        <ul class="text-sm text-yellow-700 space-y-1">
          <li>1. 首先点击"检查 MSW 状态"查看 MSW 是否正确加载</li>
          <li>2. 然后点击"运行 API 测试"测试所有 API 端点</li>
          <li>3. 查看浏览器开发者工具的 Console 和 Network 标签</li>
          <li>4. 如果 MSW 工作正常，应该看到模拟的 API 响应数据</li>
          <li>5. 如果看到网络错误，说明 MSW 没有正确拦截请求</li>
        </ul>
      </div>
    </div>
  );
}
