import { createSignal, createResource, Show } from 'solid-js';

// 简单的 API 测试函数
const testAPI = async (endpoint: string) => {
  try {
    const response = await fetch(endpoint);
    const data = await response.json();
    return {
      success: true,
      status: response.status,
      data: data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
};

export default function MSWTestPage() {
  const [selectedEndpoint, setSelectedEndpoint] = createSignal('/api/account/get/');
  const [testResult] = createResource(selectedEndpoint, testAPI);

  const endpoints = [
    { path: '/api/account/get/', name: '获取账户信息' },
    { path: '/api/account/setting/ns_user/get/', name: '获取账户设置' },
    { path: '/api/user/amount/', name: '获取钱包信息' },
    { path: '/api/account/unauthorized/', name: '未授权访问（错误测试）' },
    { path: '/api/network/error/', name: '网络错误（错误测试）' }
  ];

  return (
    <div class="p-6 max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">MSW API 测试页面</h1>
      
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-2">测试说明</h2>
        <p class="text-blue-700">
          这个页面用于测试 MSW (Mock Service Worker) 是否正常拦截 API 请求。
          如果 MSW 正常工作，您应该能看到模拟的 API 响应数据。
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：API 端点选择 */}
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">选择 API 端点</h2>
          <div class="space-y-2">
            {endpoints.map((endpoint) => (
              <button
                class={`w-full text-left p-3 rounded border transition-colors ${
                  selectedEndpoint() === endpoint.path
                    ? 'bg-blue-100 border-blue-300 text-blue-800'
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
                onClick={() => setSelectedEndpoint(endpoint.path)}
              >
                <div class="font-medium">{endpoint.name}</div>
                <div class="text-sm text-gray-600">{endpoint.path}</div>
              </button>
            ))}
          </div>
        </div>

        {/* 右侧：测试结果 */}
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4">测试结果</h2>
          
          <div class="mb-4">
            <span class="text-sm text-gray-600">当前测试端点：</span>
            <code class="bg-gray-100 px-2 py-1 rounded text-sm ml-2">
              {selectedEndpoint()}
            </code>
          </div>

          <Show when={testResult.loading}>
            <div class="flex items-center space-x-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span class="text-gray-600">正在测试 API...</span>
            </div>
          </Show>

          <Show when={testResult.error}>
            <div class="bg-red-50 border border-red-200 rounded p-4">
              <h3 class="font-medium text-red-800 mb-2">请求失败</h3>
              <p class="text-red-700 text-sm">{testResult.error?.message}</p>
            </div>
          </Show>

          <Show when={testResult()}>
            <div class={`border rounded p-4 ${
              testResult()?.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div class="flex items-center justify-between mb-3">
                <h3 class={`font-medium ${
                  testResult()?.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {testResult()?.success ? '✅ 请求成功' : '❌ 请求失败'}
                </h3>
                <span class={`px-2 py-1 rounded text-sm font-mono ${
                  testResult()?.status === 200 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {testResult()?.status || 'N/A'}
                </span>
              </div>
              
              <div class="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-auto">
                <pre>{JSON.stringify(testResult()?.data || testResult()?.error, null, 2)}</pre>
              </div>
            </div>
          </Show>
        </div>
      </div>

      {/* MSW 状态检查 */}
      <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">MSW 状态检查</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div class="bg-white p-3 rounded border">
            <div class="font-medium text-gray-800">Service Worker</div>
            <div class="text-gray-600">
              {typeof navigator !== 'undefined' && 'serviceWorker' in navigator 
                ? '✅ 支持' 
                : '❌ 不支持'}
            </div>
          </div>
          <div class="bg-white p-3 rounded border">
            <div class="font-medium text-gray-800">开发环境</div>
            <div class="text-gray-600">
              {import.meta.env.DEV ? '✅ 开发模式' : '❌ 生产模式'}
            </div>
          </div>
          <div class="bg-white p-3 rounded border">
            <div class="font-medium text-gray-800">MSW 文件</div>
            <div class="text-gray-600">
              检查 /mockServiceWorker.js
            </div>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-2 text-yellow-800">如何验证 MSW 是否工作</h3>
        <ul class="text-sm text-yellow-700 space-y-1">
          <li>1. 打开浏览器开发者工具的 Console 标签</li>
          <li>2. 查找 "🔶 MSW enabled for development" 消息</li>
          <li>3. 在 Network 标签中查看请求是否被 Service Worker 拦截</li>
          <li>4. 测试不同的 API 端点，查看是否返回模拟数据</li>
          <li>5. 如果看到真实的 API 响应数据，说明 MSW 正常工作</li>
        </ul>
      </div>
    </div>
  );
}
