import { createSignal } from 'solid-js';

export default function SimpleTest() {
  const [result, setResult] = createSignal('点击按钮开始测试...');

  const testMSW = async () => {
    setResult('🔄 正在测试...');
    console.log('🔄 开始测试 MSW');
    
    try {
      // 测试一个简单的 API 端点
      const response = await fetch('/api/account/get/');
      const data = await response.json();
      
      console.log('✅ API 响应:', data);
      setResult(`✅ 成功！状态: ${response.status}, 数据: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error('❌ API 错误:', error);
      setResult(`❌ 失败: ${error}`);
    }
  };

  const checkMSW = async () => {
    setResult('🔍 检查 MSW 状态...');
    console.log('🔍 检查 MSW 状态');
    
    try {
      // 检查 Service Worker
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        console.log('📋 Service Worker 注册:', registrations);
        
        // 尝试加载 MSW
        const { worker } = await import('../mocks/browser');
        console.log('✅ MSW worker 加载成功:', worker);
        
        setResult(`✅ MSW 检查完成！Service Worker 注册数: ${registrations.length}`);
      } else {
        setResult('❌ 浏览器不支持 Service Worker');
      }
    } catch (error) {
      console.error('❌ MSW 检查失败:', error);
      setResult(`❌ MSW 检查失败: ${error}`);
    }
  };

  return (
    <div class="p-8 max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold mb-6">MSW 简单测试</h1>
      
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">测试控制</h2>
        <div class="space-x-4">
          <button
            onClick={checkMSW}
            class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
          >
            检查 MSW 状态
          </button>
          <button
            onClick={testMSW}
            class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700"
          >
            测试 API 请求
          </button>
        </div>
      </div>

      <div class="bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-3">测试结果</h3>
        <pre class="bg-gray-900 text-green-400 p-4 rounded text-sm overflow-auto">
          {result()}
        </pre>
      </div>

      <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="font-semibold mb-2 text-yellow-800">使用说明</h3>
        <ol class="text-sm text-yellow-700 space-y-1">
          <li>1. 打开浏览器开发者工具的 Console 标签</li>
          <li>2. 点击"检查 MSW 状态"查看 MSW 是否正确加载</li>
          <li>3. 点击"测试 API 请求"发送测试请求</li>
          <li>4. 查看控制台日志和测试结果</li>
        </ol>
      </div>
    </div>
  );
}
