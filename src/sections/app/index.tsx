import {
  createEffect,
  VoidComponent,
  createMemo,
  untrack,
  createComponent,
  getOwner,
  runWithOwner
} from "solid-js";
import { Module } from "../modules";
import { env } from "@/common/storage/env";
import { getHttp } from "@/common/network/http";
import { setting } from "@/common/storage/setting";
import { routerUtils } from "@/common/router";
import { DialogStackRouter } from "@/common/router/dialog-stack";
import { ToastProvider } from "@/common/toast";
import { PopProvider } from "@/common/pop";
import { RouteDefinition, Router, RouteSectionProps } from "@solidjs/router";
import { isLayoutRoute, resolveRouteDefinition } from "@/common/router";
import { getScrollbarWidth } from "@/common/utils/scrollbar";
import { app } from "@/common/app";
import { GlobalComponents, GlobalComponentsCtx } from "@/context/global-component-context";
import { NavigatorProvider } from "@/context/navigator-context";
import { PreloadScheduler } from "@/common/router/preload";

interface AppBaseProps {
  globalComponents?: GlobalComponents;
  modules?: Module[];
  langs?: string[];
  lang?: string;
}

const hash = location.hash;
if (hash.startsWith("#/")) {
  history.replaceState(history.state, "", hash.slice(1));
}

function fixSolideRouterHashScrollBug() {
  const scrollTo = window.scrollTo;
  window.scrollTo = ((x: number, y: number) => {
    if (y === 0 && x === 0) {
      return;
    }
    scrollTo(x, y);
  }) as typeof window.scrollTo;
}

fixSolideRouterHashScrollBug();

export const AppBase: VoidComponent<AppBaseProps> = props => {
  initEnv();
  routerUtils.syncLang(props.langs || Object.keys(env.langs) || [], props.lang);
  const routes = createMemo(() =>
    untrack(() => {
      const dialogRoutes: RouteDefinition[] = [],
        allRoutes = props.modules?.map(module => module.routes()).flat() || [],
        firstRoute = allRoutes[0];
      let routeHandler;
      if (firstRoute && isLayoutRoute(firstRoute)) {
        const childRoutes = resolveRouteDefinition(firstRoute.children || []);
        (firstRoute.children = childRoutes),
          (routeHandler = (route: RouteDefinition) => {
            childRoutes.unshift(route);
          });
      } else
        routeHandler = (route: RouteDefinition) => {
          dialogRoutes.unshift(route);
        };

      allRoutes.forEach(route => {
        let routeInfo;
        processRouteInfo(route);
        if (!((routeInfo = route.info) != null && routeInfo.dialog) && !isLayoutRoute(route)) {
          routeHandler(route);
        } else {
          dialogRoutes.push(route);
        }
      });
      return dialogRoutes;
    })
  );
  function processRouteInfo(route: RouteDefinition) {
    if (route.info && route.children instanceof Array) {
      route.children.forEach(childRoute => {
        childRoute.info = {
          ...route.info,
          ...childRoute.info
        };
        processRouteInfo(childRoute);
      });
    }
  }
  setTimeout(() => {
    createEffect(() => {
      document.documentElement.classList.toggle("scroll-bar", getScrollbarWidth() > 0);
    });
  }, 3e3);
  function initializeModules() {
    const owner = getOwner();

    props.modules?.forEach(module => {
      if (module.events) {
        Object.entries(module.events).forEach(([eventName, handler]) => {
          app.on(eventName, async (...args: any[]) => {
            const eventHandler = await handler();
            runWithOwner(owner, () => {
              eventHandler(...args);
            });
          });
        });
      }
      if (module.init) {
        runWithOwner(owner, module.init);
      }
    });

    return null;
  }

  const langPrefix = routerUtils.getLangPrefix(setting.lang);

  const RootComponent = () => {
    return (
      <GlobalComponentsCtx.Provider value={props.globalComponents || ({} as GlobalComponents)}>
        <NavigatorProvider base={langPrefix}>
          <>
            {createMemo(() => untrack(initializeModules))}
            <PreloadScheduler routes={routes()} />
            <DialogStackRouter routes={routes()} />
            <ToastProvider />
            <PopProvider />
            {createMemo(() => props.children)}
          </>
        </NavigatorProvider>
      </GlobalComponentsCtx.Provider>
    );
  };

  return (
    <Router root={RootComponent} base={langPrefix} preload={false}>
      {routes()}
    </Router>
  );
};

function initEnv() {
  createEffect(() => {
    document.documentElement.classList.toggle("dark", setting.darken);
    const ele = document.querySelector('meta[name="theme-color"]');
    if (ele) {
      ele.setAttribute("content", setting.darken ? "#232626" : "#f4f4f4");
    }
  });
  createEffect(() => {
    document.documentElement.classList.toggle("lowend", env.isLowEndDevice);
  });
  createEffect(() => {
    document.documentElement.classList.toggle("pc", !env.mobile);
  });
  createEffect(() => (document.documentElement.style.fontSize = env.remScale * 16 + "px"));
  createEffect(() => {
    getHttp().defaults.headers.common["Accept-Language"] = setting.lang;
  });
}
