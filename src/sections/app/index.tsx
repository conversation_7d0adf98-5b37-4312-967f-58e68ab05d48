import { createEffect, VoidComponent } from "solid-js";
import { Module } from "../modules";
import { env } from "@/common/storage/env";
import { getHttp } from "@/common/network/http";
import { setting } from "@/common/storage/setting";

interface AppBaseProps {
  globalComponents?: {
    GameRecommend: {
      (params: any): any;
      preload: () => any;
    };
    gameTabs: () => any;
    selectCurrency: () => any;
  };
  modules?: Module[];
  langs?: string[];
  lang?: string;
}

export const AppBase: VoidComponent<AppBaseProps> = props => {
  initEnv();
  return <div>11</div>;
};

function initEnv() {
  createEffect(() => {
    document.documentElement.classList.toggle("dark", setting.darken);
    const ele = document.querySelector('meta[name="theme-color"]');
    if (ele) {
      ele.setAttribute("content", setting.darken ? "#232626" : "#f4f4f4");
    }
  });
  createEffect(() => {
    document.documentElement.classList.toggle("lowend", env.isLowEndDevice);
  });
  createEffect(() => {
    document.documentElement.classList.toggle("pc", !env.mobile);
  });
  createEffect(() => (document.documentElement.style.fontSize = env.remScale * 16 + "px"));
  createEffect(() => {
    getHttp().defaults.headers.common["Accept-Language"] = setting.lang;
  });
}
