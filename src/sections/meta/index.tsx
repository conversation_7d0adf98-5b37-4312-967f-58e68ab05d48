import { <PERSON>, <PERSON>a, <PERSON> } from "@solidjs/meta";
import { createMemo, Show } from "solid-js";
import { isServer } from "solid-js/web";
import { env } from "@/common/storage/env";
import { getHostType } from "@/common/utils";

const defaultMetaConfig = {
  title: "BC.GAME: Crypto Casino Games & Casino Slot Games - Crypto Gambling",
  keywords:
    "Crypto Casino Games, Bitcoin Crash Game, Bitcoin Gambling Games, Crypto Games, Bc Game, Bcgame, Crypto Gambling Games, Play Live Casino Online Free, Best Crypto Casino Games, Best Crypto Games, Online Crypto Casino Games, Online Blockchain Games, Online Casino Slot Games",
  description:
    "Best Crypto Casino.Biggest crypto crash game.Provably fair & Live dealer.10000 slot games.Unique bonus & free lucky spins.BTC,ETH,DOGE,TRX,XRP,UNI,defi tokens supported fast withdrawals and Profitable vault."
};

const restrictedMetaConfig = {
  title: "BC.GAME: Casino Games & Slot Games - Gambling",
  keywords:
    "Casino Games, Crash Game, Gambling Games, Bc Game, Bcgame, Play Live Casino Online Free, Best Casino Games, Best Games, Online Casino Games, Online Casino Slot Games",
  description:
    "Best Casino.Biggest crash game.Provably fair & Live dealer.10000 slot games.Unique bonus & free lucky spins. supported fast withdrawals and Profitable vault."
};
export function MetaTags() {
  const hostname = env.host;
  const { isBrHost, isBzHost, isBrAuditHost, isSpHost } = getHostType(hostname);
  const hostCond = {
    ng: hostname === "bcbet.ng",
    br: isBrHost
  };
  const meta = hostCond.ng ? restrictedMetaConfig : getAdaptedMetadata(hostname, defaultMetaConfig);

  const canonicalUrl = getCanonicalUrl();
  const shareMeta = {
    title: meta.title,
    description: meta.description,
    image: `https://${hostname}/share_image.jpg`,
    canonical: getCanonicalUrl(),
    host: hostname,
    product: "product",
    twitterCard: "summary_large_image"
  };

  const getFaviconPath = (name: string) =>
    isSpHost
      ? `/favicon/87/${name}`
      : isBrAuditHost
        ? `/favicon/bs/${name}`
        : isBzHost
          ? `/favicon/bz/${name}`
          : `/favicon/bc/${name}`;

  return (
    <>
      <Title>{meta.title}</Title>
      <Meta name="keywords">{meta.keywords}</Meta>
      <Meta name="description">{meta.description}</Meta>
      <Link rel="canonical" href={canonicalUrl} />
      <Link rel="icon" href={getFaviconPath("favicon.ico")} />
      <Link rel="icon" sizes="32x32" href={getFaviconPath("favicon-32x32.png")} />
      <Link rel="icon" sizes="16x16" href={getFaviconPath("favicon-16x16.png")} />
      <Meta name="google-site-verification" content="X4Qt1WVSNQq-oYZqpSlLxjhLmMXto-Fvzg67xe076Fo" />
      <Show when={!hostCond.ng}>
        <Meta name="og:title" content={shareMeta.title} />
        <Meta name="og:description" content={shareMeta.description} />
        <Meta name="og:image" content={shareMeta.image} />
        <Meta name="og:type" content={shareMeta.product} />
        <Meta name="og:url" content={shareMeta.canonical} />
        <Meta name="twitter:card" content={shareMeta.twitterCard} />
      </Show>
      {createMemo(() => (
        <>{i18nLinks(hostname, isServer ? "" : window.location.pathname, env.langs)}</>
      ))}
    </>
  );
}

function getCanonicalUrl() {
  if (isServer) {
    return "";
  }
  const pathname = location.pathname;
  return `https://${env.buildHost?.toLocaleLowerCase()}${pathname.replace(/\/$/, "")}`;
}

function getAdaptedMetadata(hostname: string, defaultConfig = defaultMetaConfig) {
  let config = {
    ...defaultConfig
  };
  const { isBrAuditHost, isBrHost, isNg2Host, isNgHost, isKenyaHost, isSpHost, isBzHost } =
    getHostType(hostname);

  if (
    isBrAuditHost ||
    isBrHost ||
    isNg2Host ||
    isNgHost ||
    isKenyaHost ||
    env.host.includes("bcgame.ng")
  ) {
    config = Object.assign(config, restrictedMetaConfig);
  }

  if (isSpHost || isBrAuditHost || isBzHost) {
    config.title = config.title.replace(/BC.GAME/g, env.host.toUpperCase());
    config.keywords = config.keywords.replace(/BC.GAME/g, env.host.toUpperCase());
    config.description = config.description.replace(/BC.GAME/g, env.host.toUpperCase());
  }
  return config;
}

function i18nLinks(host: string, pathname: string, langs = env.langs) {
  return Object.keys(langs).map(lang => {
    const href = `https://${encodeURI(host)}${
      lang === "en" ? "" : "/" + encodeURI(lang)
    }${encodeURI(pathname)}`;
    return <Link rel="alternate" hreflang={lang === "en" ? "x-default" : lang} href={href} />;
  });
}
