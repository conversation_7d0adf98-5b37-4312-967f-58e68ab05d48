import { describe, it, expect, beforeAll, afterAll, afterEach, vi } from 'vitest';
import { server } from '@/mocks/server';
import { mockAccount, mockAccountSetting } from '@/mocks/handlers';

// 模拟 axios 请求，因为我们没有真正的 MSW 拦截
const mockAxios = {
  get: vi.fn(),
  post: vi.fn()
};

// 模拟 getAccount 函数
const getAccount = async () => {
  const response = await mockAxios.get('/api/account/get/');
  if (response.data.code !== 0) {
    throw new Error(response.data.msg);
  }
  return response.data.data;
};

// 模拟 getAccountSetting 函数
const getAccountSetting = async () => {
  const response = await mockAxios.get('/api/account/setting/ns_user/get/');
  if (response.data.code !== 0) {
    throw new Error(response.data.msg);
  }
  return response.data.data;
};

describe('Account Service with MSW', () => {
  beforeAll(() => {
    if (server && typeof server.listen === 'function') {
      server.listen();
      console.log('🚀 MSW server started for account tests');
    }
  });

  afterEach(() => {
    if (server && typeof server.resetHandlers === 'function') {
      server.resetHandlers();
    }
    vi.clearAllMocks();
  });

  afterAll(() => {
    if (server && typeof server.close === 'function') {
      server.close();
      console.log('🛑 MSW server stopped');
    }
  });

  describe('getAccount', () => {
    it('should fetch account data successfully', async () => {
      // 模拟成功响应
      mockAxios.get.mockResolvedValue({
        data: {
          code: 0,
          data: mockAccount,
          msg: 'success'
        }
      });

      const account = await getAccount();
      
      expect(account).toEqual(mockAccount);
      expect(account.login).toBe(true);
      expect(account.username).toBe('testuser');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/account/get/');
    });

    it('should handle API error', async () => {
      // 模拟错误响应
      mockAxios.get.mockResolvedValue({
        data: {
          code: 500,
          data: null,
          msg: 'Internal server error'
        }
      });

      await expect(getAccount()).rejects.toThrow('Internal server error');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/account/get/');
    });
  });

  describe('getAccountSetting', () => {
    it('should fetch account settings successfully', async () => {
      // 模拟成功响应
      mockAxios.get.mockResolvedValue({
        data: {
          code: 0,
          data: mockAccountSetting,
          msg: 'success'
        }
      });

      const settings = await getAccountSetting();
      
      expect(settings).toEqual(mockAccountSetting);
      expect(settings.theme).toBe('dark');
      expect(settings.language).toBe('en');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/account/setting/ns_user/get/');
    });

    it('should handle API error', async () => {
      // 模拟错误响应
      mockAxios.get.mockResolvedValue({
        data: {
          code: 404,
          data: null,
          msg: 'Settings not found'
        }
      });

      await expect(getAccountSetting()).rejects.toThrow('Settings not found');
    });
  });

  describe('MSW Integration', () => {
    it('should demonstrate MSW handler usage', () => {
      // 演示如何使用 MSW 处理器
      if (server && typeof server.use === 'function') {
        // 这里可以动态添加新的处理器
        server.use(/* 新的处理器 */);
      }
      
      expect(server).toBeDefined();
      expect(typeof server.use).toBe('function');
    });

    it('should show available mock data', () => {
      // 展示可用的 mock 数据
      expect(mockAccount).toEqual({
        login: true,
        id: '12345',
        username: 'testuser',
        email: '<EMAIL>',
        chatRoomPermission: {
          canSend: true,
          canReceive: true
        }
      });

      expect(mockAccountSetting).toEqual({
        theme: 'dark',
        language: 'en',
        notifications: true
      });
    });
  });
});
