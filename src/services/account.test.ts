import { describe, it, expect, beforeEach } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '@/mocks/server';
import { mockAccount, mockAccountSetting } from '@/mocks/handlers';
import { getAccount, getAccountSetting, saveSetting, remoteSetting } from './account';

describe('Account Service', () => {
  beforeEach(() => {
    // 重置 remoteSetting
    remoteSetting.value = '';
  });

  describe('getAccount', () => {
    it('should fetch account data successfully', async () => {
      const account = await getAccount();
      
      expect(account).toEqual(mockAccount);
      expect(account.login).toBe(true);
      expect(account.username).toBe('testuser');
    });

    it('should remove chatRoomPermission if it is falsy', async () => {
      // Mock 一个没有 chatRoomPermission 的响应
      server.use(
        http.get('/api/account/get/', () => {
          const accountWithoutPermission = { ...mockAccount };
          delete accountWithoutPermission.chatRoomPermission;
          return HttpResponse.json({
            code: 0,
            data: accountWithoutPermission,
            msg: 'success'
          });
        })
      );

      const account = await getAccount();
      expect(account.chatRoomPermission).toBeUndefined();
    });

    it('should handle API error', async () => {
      server.use(
        http.get('/api/account/get/', () => {
          return HttpResponse.json({
            code: 500,
            data: null,
            msg: 'Internal server error'
          });
        })
      );

      await expect(getAccount()).rejects.toThrow('Internal server error');
    });
  });

  describe('getAccountSetting', () => {
    it('should fetch account settings successfully', async () => {
      const settings = await getAccountSetting();
      
      expect(settings).toEqual(mockAccountSetting);
      expect(settings.theme).toBe('dark');
      expect(settings.language).toBe('en');
    });

    it('should handle API error', async () => {
      server.use(
        http.get('/api/account/setting/ns_user/get/', () => {
          return HttpResponse.json({
            code: 404,
            data: null,
            msg: 'Settings not found'
          });
        })
      );

      await expect(getAccountSetting()).rejects.toThrow('Settings not found');
    });
  });

  describe('saveSetting', () => {
    const testSetting = JSON.stringify({ theme: 'light', language: 'zh' });

    it('should save settings when user is logged in', async () => {
      // 首先获取账户信息以设置 preAccountReq
      await getAccount();
      
      const result = await saveSetting(testSetting);
      expect(remoteSetting.value).toBe(testSetting);
      expect(result).toBeDefined();
    });

    it('should not save if setting is same as remote setting', async () => {
      remoteSetting.value = testSetting;
      
      const result = await saveSetting(testSetting);
      expect(result).toBeUndefined();
    });

    it('should not save if user is not logged in', async () => {
      // Mock 未登录用户
      server.use(
        http.get('/api/account/get/', () => {
          return HttpResponse.json({
            code: 0,
            data: { ...mockAccount, login: false },
            msg: 'success'
          });
        })
      );

      await getAccount();
      const result = await saveSetting(testSetting);
      expect(result).toBeUndefined();
    });

    it('should handle save API error', async () => {
      await getAccount();
      
      server.use(
        http.post('/api/account/setting/ns_user/reset/', () => {
          return HttpResponse.json({
            code: 500,
            data: null,
            msg: 'Failed to save settings'
          });
        })
      );

      await expect(saveSetting(testSetting)).rejects.toThrow('Failed to save settings');
    });
  });
});
