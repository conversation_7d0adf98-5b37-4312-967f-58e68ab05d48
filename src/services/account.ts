import { getHttp } from "@/common/network/http";
import { Account, AccountSetting } from "@/types/account";
import { Wallet } from "@/types/wallet";

export let preAccountReq: Promise<Account>;
export async function getAccount() {
  const resp = getHttp().get<any, Account>("/account/get/");
  preAccountReq = resp;
  const data = await resp;
  if (!data.chatRoomPermission) {
    delete data.chatRoomPermission;
  }
  return data;
}

export async function getAccountSetting() {
  const resp = await getHttp().get<any, AccountSetting>("/account/setting/ns_user/get/");
  return resp;
}

export const remoteSetting = { value: "" };

export async function saveSetting(setting: string) {
  // 若 remoteSetting 存在，且与当前设置相同，无需保存
  if (remoteSetting.value && setting === remoteSetting.value) {
    return;
  }

  // 更新 remoteSetting 本地缓存
  remoteSetting.value = setting;

  // 无账户信息，终止执行
  if (!preAccountReq) {
    return;
  }

  const account = await preAccountReq;

  // 未登录状态，不提交设置
  if (!account.login) {
    return;
  }
  return getHttp().post("/account/setting/ns_user/reset/", JSON.parse(setting));
}
