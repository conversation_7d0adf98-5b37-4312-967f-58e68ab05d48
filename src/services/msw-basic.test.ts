import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { server } from '@/mocks/server';

describe('MSW Basic Test', () => {
  beforeAll(() => {
    if (server) {
      server.listen();
      console.log('MSW server started for tests');
    } else {
      console.log('MSW server not available');
    }
  });

  afterAll(() => {
    if (server) {
      server.close();
      console.log('MSW server closed');
    }
  });

  it('should have MSW server available', () => {
    console.log('Server object:', server);
    // 这个测试只是检查服务器对象是否存在
    // 在某些环境中可能为 null，这是正常的
    expect(typeof server).toBeDefined();
  });

  it('should be able to make a simple fetch request', async () => {
    try {
      // 尝试一个简单的 fetch 请求
      const response = await fetch('/api/account/get/');
      console.log('Response status:', response.status);
      
      if (server) {
        // 如果 MSW 工作，应该得到 200 状态
        expect(response.status).toBe(200);
        const data = await response.json();
        console.log('Response data:', data);
        expect(data).toBeDefined();
      } else {
        // 如果没有 MSW，请求可能失败，这是预期的
        console.log('No MSW server, request may fail');
      }
    } catch (error) {
      console.log('Fetch error (expected without MSW):', error);
      // 如果没有 MSW，这是预期的错误
      if (!server) {
        expect(error).toBeDefined();
      } else {
        throw error;
      }
    }
  });
});
