import { describe, it, expect, beforeAll, afterAll, afterEach, vi } from 'vitest';
import { server } from '@/mocks/server';
import { mockAccount, mockAccountSetting, mockWallet } from '@/mocks/handlers';

// 模拟一个简单的 HTTP 客户端来测试 MSW 功能
const mockHttpClient = {
  get: vi.fn(),
  post: vi.fn()
};

// 模拟实际的服务函数
const accountService = {
  async getAccount() {
    const response = await mockHttpClient.get('/api/account/get/');
    return response.data;
  },
  
  async getSettings() {
    const response = await mockHttpClient.get('/api/account/setting/ns_user/get/');
    return response.data;
  },
  
  async saveSettings(settings: any) {
    const response = await mockHttpClient.post('/api/account/setting/ns_user/reset/', settings);
    return response.data;
  }
};

const walletService = {
  async getWallet() {
    const response = await mockHttpClient.get('/api/user/amount/');
    return response.data;
  }
};

describe('MSW 功能测试', () => {
  beforeAll(() => {
    if (server && typeof server.listen === 'function') {
      server.listen();
      console.log('🚀 MSW 服务器启动成功');
    }
  });

  afterEach(() => {
    if (server && typeof server.resetHandlers === 'function') {
      server.resetHandlers();
    }
    vi.clearAllMocks();
  });

  afterAll(() => {
    if (server && typeof server.close === 'function') {
      server.close();
      console.log('🛑 MSW 服务器关闭');
    }
  });

  describe('账户服务测试', () => {
    it('应该成功获取账户信息', async () => {
      // 模拟成功的 API 响应
      mockHttpClient.get.mockResolvedValue({
        data: {
          code: 0,
          data: mockAccount,
          msg: 'success'
        }
      });

      const result = await accountService.getAccount();
      
      expect(result.code).toBe(0);
      expect(result.data).toEqual(mockAccount);
      expect(result.data.username).toBe('testuser');
      expect(result.data.login).toBe(true);
      expect(mockHttpClient.get).toHaveBeenCalledWith('/api/account/get/');
    });

    it('应该成功获取账户设置', async () => {
      mockHttpClient.get.mockResolvedValue({
        data: {
          code: 0,
          data: mockAccountSetting,
          msg: 'success'
        }
      });

      const result = await accountService.getSettings();
      
      expect(result.code).toBe(0);
      expect(result.data).toEqual(mockAccountSetting);
      expect(result.data.theme).toBe('dark');
      expect(result.data.language).toBe('en');
    });

    it('应该成功保存账户设置', async () => {
      const newSettings = { theme: 'light', language: 'zh' };
      
      mockHttpClient.post.mockResolvedValue({
        data: {
          code: 0,
          data: { success: true },
          msg: 'Settings saved successfully'
        }
      });

      const result = await accountService.saveSettings(newSettings);
      
      expect(result.code).toBe(0);
      expect(result.data.success).toBe(true);
      expect(mockHttpClient.post).toHaveBeenCalledWith('/api/account/setting/ns_user/reset/', newSettings);
    });
  });

  describe('钱包服务测试', () => {
    it('应该成功获取钱包信息', async () => {
      mockHttpClient.get.mockResolvedValue({
        data: {
          code: 0,
          data: mockWallet,
          msg: 'success'
        }
      });

      const result = await walletService.getWallet();
      
      expect(result.code).toBe(0);
      expect(result.data).toEqual(mockWallet);
      expect(result.data.balance).toBe(1000.50);
      expect(result.data.currency).toBe('USD');
      expect(result.data.available).toBe(1000.50);
      expect(result.data.frozen).toBe(0);
    });
  });

  describe('错误场景测试', () => {
    it('应该处理 API 错误响应', async () => {
      mockHttpClient.get.mockResolvedValue({
        data: {
          code: 500,
          data: null,
          msg: 'Internal server error'
        }
      });

      const result = await accountService.getAccount();
      
      expect(result.code).toBe(500);
      expect(result.data).toBeNull();
      expect(result.msg).toBe('Internal server error');
    });

    it('应该处理网络错误', async () => {
      mockHttpClient.get.mockRejectedValue(new Error('Network error'));

      await expect(accountService.getAccount()).rejects.toThrow('Network error');
    });
  });

  describe('MSW 服务器状态测试', () => {
    it('MSW 服务器应该正常工作', () => {
      expect(server).toBeDefined();
      expect(typeof server.listen).toBe('function');
      expect(typeof server.close).toBe('function');
      expect(typeof server.resetHandlers).toBe('function');
      expect(typeof server.use).toBe('function');
    });

    it('应该能够动态添加处理器', () => {
      if (server && typeof server.use === 'function') {
        // 测试动态添加处理器的功能
        server.use(/* 这里可以添加新的处理器 */);
        expect(true).toBe(true); // 如果没有错误，测试通过
      }
    });
  });

  describe('Mock 数据验证', () => {
    it('Mock 账户数据应该符合预期格式', () => {
      expect(mockAccount).toHaveProperty('login');
      expect(mockAccount).toHaveProperty('id');
      expect(mockAccount).toHaveProperty('username');
      expect(mockAccount).toHaveProperty('email');
      expect(mockAccount).toHaveProperty('chatRoomPermission');
      
      expect(typeof mockAccount.login).toBe('boolean');
      expect(typeof mockAccount.id).toBe('string');
      expect(typeof mockAccount.username).toBe('string');
      expect(typeof mockAccount.email).toBe('string');
      expect(typeof mockAccount.chatRoomPermission).toBe('object');
    });

    it('Mock 设置数据应该符合预期格式', () => {
      expect(mockAccountSetting).toHaveProperty('theme');
      expect(mockAccountSetting).toHaveProperty('language');
      expect(mockAccountSetting).toHaveProperty('notifications');
      
      expect(typeof mockAccountSetting.theme).toBe('string');
      expect(typeof mockAccountSetting.language).toBe('string');
      expect(typeof mockAccountSetting.notifications).toBe('boolean');
    });

    it('Mock 钱包数据应该符合预期格式', () => {
      expect(mockWallet).toHaveProperty('balance');
      expect(mockWallet).toHaveProperty('currency');
      expect(mockWallet).toHaveProperty('frozen');
      expect(mockWallet).toHaveProperty('available');
      
      expect(typeof mockWallet.balance).toBe('number');
      expect(typeof mockWallet.currency).toBe('string');
      expect(typeof mockWallet.frozen).toBe('number');
      expect(typeof mockWallet.available).toBe('number');
    });
  });
});
