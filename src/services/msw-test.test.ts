import { describe, it, expect, beforeAll, afterAll, afterEach } from 'vitest';
import { server } from '@/mocks/server';

describe('MSW Integration Test', () => {
  beforeAll(() => {
    if (server && typeof server.listen === 'function') {
      server.listen();
      console.log('🚀 MSW server started for tests');
    } else {
      console.log('⚠️ MSW server not available, using fallback');
    }
  });

  afterEach(() => {
    if (server && typeof server.resetHandlers === 'function') {
      server.resetHandlers();
    }
  });

  afterAll(() => {
    if (server && typeof server.close === 'function') {
      server.close();
      console.log('🛑 MSW server stopped');
    }
  });

  it('should have MSW server object', () => {
    expect(server).toBeDefined();
    expect(typeof server).toBe('object');
  });

  it('should have required server methods', () => {
    expect(server).toHaveProperty('listen');
    expect(server).toHaveProperty('close');
    expect(server).toHaveProperty('resetHandlers');
    expect(server).toHaveProperty('use');
  });
});
