import { describe, it, expect, vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '@/mocks/server';
import { mockWallet } from '@/mocks/handlers';
import { getWallet } from './wallet';
import * as accountService from './account';

describe('Wallet Service', () => {
  describe('getWallet', () => {
    it('should fetch wallet data successfully', async () => {
      const wallet = await getWallet();
      
      expect(wallet).toEqual(mockWallet);
      expect(wallet.balance).toBe(1000.50);
      expect(wallet.currency).toBe('USD');
      expect(wallet.available).toBe(1000.50);
    });

    it('should wait for account request if it exists', async () => {
      // Mock preAccountReq
      const mockAccountPromise = Promise.resolve({
        login: true,
        id: '12345',
        username: 'testuser',
        email: '<EMAIL>'
      });
      
      // 使用 vi.spyOn 来 mock preAccountReq
      vi.spyOn(accountService, 'preAccountReq', 'get').mockReturnValue(mockAccountPromise);
      
      const wallet = await getWallet();
      expect(wallet).toEqual(mockWallet);
    });

    it('should handle API error', async () => {
      server.use(
        http.get('/api/user/amount/', () => {
          return HttpResponse.json({
            code: 500,
            data: null,
            msg: 'Failed to fetch wallet data'
          });
        })
      );

      await expect(getWallet()).rejects.toThrow('Failed to fetch wallet data');
    });

    it('should handle network error', async () => {
      server.use(
        http.get('/api/user/amount/', () => {
          return HttpResponse.error();
        })
      );

      await expect(getWallet()).rejects.toThrow();
    });

    it('should handle different wallet currencies', async () => {
      const eurWallet = { ...mockWallet, currency: 'EUR', balance: 850.25 };
      
      server.use(
        http.get('/api/user/amount/', () => {
          return HttpResponse.json({
            code: 0,
            data: eurWallet,
            msg: 'success'
          });
        })
      );

      const wallet = await getWallet();
      expect(wallet.currency).toBe('EUR');
      expect(wallet.balance).toBe(850.25);
    });
  });
});
