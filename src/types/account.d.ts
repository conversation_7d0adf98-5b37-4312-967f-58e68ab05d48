// {
//     "showOnlineStatus": true,
//     "userId": 77335151,
//     "name": "Pervnfunkvcc",
//     "email": "<EMAIL>",
//     "emailVerified": false,
//     "avatar": "/avatar/77335151/s",
//     "passkeyHasSet": false,
//     "passwordExist": true,
//     "open": 0,
//     "google2StepAuth": false,
//     "gameable": true,
//     "gameBlockTime": 0,
//     "safePwd": false,
//     "createTime": 1748514497928,
//     "hasPhone": false,
//     "phone": null,
//     "everSeenDevice": false,
//     "appChannelId": 1,
//     "newDevice": false,
//     "twoStepVerifyTypes": null,
//     "verifyFlowId": null,
//     "visitor": false,
//     "login": true,
//     "chatRoomPermission": {
//         "blockable": false,
//         "deleteable": false,
//         "vipable": false,
//         "blockLevel": 0
//     },
//     "loginSource": "",
//     "uniqueUid": 14315048662416,
//     "uid": 14315048662416,
//     "deviceId": "a4721e2ef37d1dd1ea785cfbb2d4a734aad704e9eedc83322bbd236d7cea301a394a7a4b5eb07c889c508b780c7a0b5518be806dc6df84212803880fee66cce3",
//     "vipLevel": 0,
//     "currXP": 0,
//     "levelStartXP": 0,
//     "levelEndXP": 1,
//     "currentInvitationCode": "1hlz5udn6",
//     "areaCode": "IN",
//     "ip": "***********",
//     "showable": true,
//     "areaAlert": false,
//     "bonusCurrencyName": "BCD",
//     "invitationUrl": "https://bc.game/?i=1hlz5udn6&utm_source=1hlz5udn6",
//     "acceptPromotion": true,
//     "betCoVersion": "CLOSE_LIVE",
//     "rayId": "9fbd0a576dea82ef-*************"
// }

export interface AccountBase {
  showOnlineStatus: boolean;
  userId: number;
  name: string;
  email: string;
  emailVerified: boolean;
  avatar: string;
  passkeyHasSet: boolean;
  passwordExist: boolean;
  open: number;
  google2StepAuth: boolean;
  gameable: boolean;
  gameBlockTime: number;
  safePwd: boolean;
  createTime: number;
  hasPhone: boolean;
  phone?: string | null;
  everSeenDevice: boolean;
  appChannelId: number;
  newDevice: boolean;
  twoStepVerifyTypes?: string | null;
  verifyFlowId?: string | null;
  visitor: boolean;
  login: boolean;
  chatRoomPermission?: ChatRoomPermission;
  loginSource: string;
  uniqueUid: number;
  uid: number;
  deviceId: string;
  vipLevel: number;
  currXP: number;
  levelStartXP: number;
  levelEndXP: number;
  currentInvitationCode: string;
  areaCode: string;
  ip: string;
  showable: boolean;
  areaAlert: boolean;
  bonusCurrencyName: string;
  invitationUrl: string;
  acceptPromotion: boolean;
  betCoVersion: string;
  rayId: string;
}

export interface Account extends AccountBase {
  serverTimestemp?: number;
}

export interface ChatRoomPermission {
  blockable: boolean;
  deleteable: boolean;
  vipable: boolean;
  blockLevel: number;
}

type AccountStore = {
  name: string;
  avatar: string;
  userId: number;
  uniqueUid: number;
  uid: number;
  createTime: number;
  kyc: number;
  gameable: boolean;
  login: boolean;
  email: string;
  loginSource: string;
  open: number;
  bonusCurrencyName: string;
  avatarUpdateTime: number;
  vipLevel: number;
  currXP: number;
  levelEndXP: number;
  levelStartXP: number;
  safePwd: boolean;
  google2StepAuth: boolean;
  hasPhone: boolean;
  chatRoomPermission: ChatRoomPermission;
  emailVerified: boolean;
  channel: string;
  currentInvitationCode: string;
  areaCode: string;
  showable: boolean;
  areaAlert: boolean;
  acceptPromotion: boolean;
  passwordExist: boolean;
  invitationUrl: string;
  serverTimestemp: number;
  ip: string;
  rayId: string;
  passkeyHasSet: boolean;
  setting: {
    currencyFullName: boolean;
    localeCurrencyName: string;
    enableLocaleCurrency: boolean;
    currencyName: string;
    currencyBonusType: string;
    hideSmallCurrency: boolean;
    lastFiatCurrency: string;
    lastCryptoCurrency: string;
    lastNftCurrency: string;
    soundEffectEnable: boolean;
    maxbetAlert: boolean;
    allowRechargeSuccessEmail: number;
    allowWithdrawSuccessEmail: number;
    lang: string;
  };
  redDot: {
    systemNotice: number;
    gameComment: number;
  };
};

export interface AccountSetting {
  currencyFullName: boolean;
  localeCurrencyName: string;
  enableLocaleCurrency: boolean;
  currencyName: string;
  currencyBonusType: string;
  hideSmallCurrency: false;
  lastFiatCurrency: string;
  lastCryptoCurrency: string;
  lastNftCurrency: string;
  soundEffectEnable: boolean;
  maxbetAlert: boolean;
  allowRechargeSuccessEmail: number;
  allowWithdrawSuccessEmail: number;
  lang: string;
}
