import Decimal from "decimal.js";

export interface AmountItem {
  lastLogId: string;
  currencyName: string;
  aliasCurrencyName: string;
  amount: string | Decimal;
  generalAmount: string | Decimal;
  bonusAmount: string | Decimal;
  groupAmount: {
    free_amount: string | Decimal;
    sports: string;
  };
  abnormal: boolean;
  sort: number;
  display: boolean;
  displayStatus: number;
  supportTx: boolean;
  useable: boolean;
  areaAble: boolean;
}

export type Wallet = AmountItem[];
