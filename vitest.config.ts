import tailwindcss from "@tailwindcss/vite";
import solid from "vite-plugin-solid";
import { defineConfig } from "vitest/config";

export default defineConfig({
  plugins: [solid(), tailwindcss()],
  resolve: {
    conditions: ["node"],
    alias: {
      "@": "/src"
    }
  },
  test: {
    environment: "jsdom",
    setupFiles: ["./src/test-setup.ts"],
    globals: true,
    environmentOptions: {
      jsdom: {
        customExportConditions: [""]
      }
    }
  },
  define: {
    global: "globalThis"
  },
  optimizeDeps: {
    include: ["msw/node"]
  }
});
